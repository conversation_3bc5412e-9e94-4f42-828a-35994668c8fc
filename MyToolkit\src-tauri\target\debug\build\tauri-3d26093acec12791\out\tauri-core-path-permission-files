["\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-3d26093acec12791\\out\\permissions\\path\\autogenerated\\default.toml"]