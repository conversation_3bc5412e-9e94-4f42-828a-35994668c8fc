{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "工具箱", "version": "1.0.0", "identifier": "com.mytoolkit.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "工具箱", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all"}, "plugins": {"clipboard-manager": {"readText": true, "writeText": true}, "dialog": {"open": true, "save": true, "message": true, "ask": true, "confirm": true}, "fs": {"readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}, "notification": {"all": true}, "shell": {"open": true}}}