pub type LocalName = :: string_cache :: Atom < LocalNameStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct LocalNameStaticSet ;
impl :: string_cache :: StaticAtomSet for LocalNameStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 12913932095322966823u64 , disps : & [(0u32 , 59u32) , (0u32 , 96u32) , (0u32 , 217u32) , (0u32 , 1u32) , (0u32 , 23u32) , (0u32 , 6u32) , (0u32 , 52u32) , (0u32 , 16u32) , (0u32 , 119u32) , (0u32 , 15u32) , (0u32 , 0u32) , (0u32 , 19u32) , (0u32 , 0u32) , (0u32 , 377u32) , (0u32 , 235u32) , (0u32 , 0u32) , (0u32 , 77u32) , (0u32 , 151u32) , (0u32 , 542u32) , (0u32 , 325u32) , (0u32 , 14u32) , (0u32 , 379u32) , (0u32 , 18u32) , (0u32 , 3u32) , (1u32 , 7u32) , (1u32 , 282u32) , (0u32 , 182u32) , (0u32 , 342u32) , (0u32 , 41u32) , (0u32 , 34u32) , (0u32 , 3u32) , (1u32 , 182u32) , (0u32 , 12u32) , (0u32 , 428u32) , (0u32 , 555u32) , (0u32 , 258u32) , (0u32 , 21u32) , (0u32 , 67u32) , (1u32 , 196u32) , (0u32 , 4u32) , (0u32 , 5u32) , (1u32 , 346u32) , (1u32 , 29u32) , (0u32 , 108u32) , (1u32 , 454u32) , (0u32 , 0u32) , (3u32 , 233u32) , (0u32 , 96u32) , (0u32 , 2u32) , (0u32 , 84u32) , (0u32 , 0u32) , (0u32 , 358u32) , (0u32 , 2u32) , (0u32 , 138u32) , (0u32 , 21u32) , (0u32 , 58u32) , (0u32 , 181u32) , (0u32 , 56u32) , (0u32 , 5u32) , (0u32 , 531u32) , (0u32 , 161u32) , (0u32 , 72u32) , (4u32 , 36u32) , (0u32 , 408u32) , (0u32 , 6u32) , (0u32 , 2u32) , (0u32 , 99u32) , (0u32 , 6u32) , (1u32 , 206u32) , (1u32 , 292u32) , (1u32 , 227u32) , (0u32 , 1u32) , (3u32 , 418u32) , (0u32 , 9u32) , (0u32 , 242u32) , (0u32 , 0u32) , (4u32 , 129u32) , (0u32 , 20u32) , (0u32 , 62u32) , (0u32 , 348u32) , (0u32 , 2u32) , (0u32 , 573u32) , (0u32 , 0u32) , (3u32 , 456u32) , (0u32 , 0u32) , (0u32 , 208u32) , (0u32 , 43u32) , (3u32 , 166u32) , (0u32 , 15u32) , (0u32 , 153u32) , (1u32 , 448u32) , (1u32 , 536u32) , (0u32 , 5u32) , (0u32 , 433u32) , (0u32 , 10u32) , (3u32 , 47u32) , (1u32 , 13u32) , (0u32 , 298u32) , (0u32 , 10u32) , (0u32 , 3u32) , (1u32 , 289u32) , (0u32 , 10u32) , (0u32 , 460u32) , (0u32 , 5u32) , (0u32 , 109u32) , (0u32 , 39u32) , (0u32 , 7u32) , (0u32 , 0u32) , (0u32 , 2u32) , (25u32 , 322u32) , (0u32 , 303u32) , (2u32 , 157u32) , (9u32 , 529u32) , (1u32 , 34u32) , (6u32 , 118u32) , (1u32 , 213u32)] , atoms : & ["patterncontentunits",
"aria-sort",
"onbeforeprint",
"thickmathspace",
"overflow",
"feoffset",
"hidefocus",
"stitchtiles",
"radialgradient",
"ondataavailable",
"altGlyphDef",
"onbeforeeditfocus",
"prefetch",
"vert-origin-y",
"subscriptshift",
"stop-opacity",
"aria-hidden",
"intercept",
"animatecolor",
"maskContentUnits",
"onfocusout",
"repeat-max",
"aria-owns",
"encoding",
"aria-required",
"surfacescale",
"irrelevant",
"feColorMatrix",
"altGlyph",
"systemlanguage",
"ononline",
"fespecularlighting",
"glyphref",
"arabic-form",
"pointsatz",
"notsubset",
"clip-rule",
"numOctaves",
"accept-charset",
"maxlength",
"aria-atomic",
"feSpotLight",
"verythinmathspace",
"spreadMethod",
"crossorigin",
"aria-selected",
"onrowexit",
"cap-height",
"fedropshadow",
"datatemplate",
"contenteditable",
"edgeMode",
"v-mathematical",
"units-per-em",
"feComposite",
"fontfamily",
"itemscope",
"color-profile",
"hreflang",
"contentscripttype",
"condition",
"onreadystatechange",
"notprsubset",
"selected",
"onrowenter",
"stroke-width",
"repeat-start",
"mphantom",
"onmouseover",
"translate",
"contentStyleType",
"font-face-name",
"textpath",
"ondragover",
"requiredExtensions",
"font-size",
"tabindex",
"aria-readonly",
"alphabetic",
"femorphology",
"language",
"maskunits",
"zoomandpan",
"controllerchange",
"stroke-dasharray",
"onbeforecopy",
"feSpecularLighting",
"feDistantLight",
"repeat-template",
"pointsAtX",
"marker-start",
"repeatDur",
"xml:lang",
"scriptsizemultiplier",
"v-hanging",
"groupalign",
"shape-rendering",
"letter-spacing",
"flood-color",
"patternUnits",
"onoffline",
"keySplines",
"feConvolveMatrix",
"direction",
"filterRes",
"onmousedown",
"plaintext",
"longdesc",
"kernelmatrix",
"spellcheck",
"onafterprint",
"scalarproduct",
"rendering-intent",
"elevation",
"stddeviation",
"columnspacing",
"stroke-dashoffset",
"datalist",
"animatemotion",
"infinity",
"aria-live",
"font-family",
"piecewise",
"stroke-linejoin",
"fecomposite",
"aria-datatype",
"feDiffuseLighting",
"cellspacing",
"metadata",
"keysplines",
"foreignobject",
"reversed",
"mozbrowser",
"externalResourcesRequired",
"animatetransform",
"markerunits",
"text-decoration",
"xchannelselector",
"onpopstate",
"pointsAtY",
"color-rendering",
"lineargradient",
"aria-level",
"otherwise",
"word-spacing",
"enable-background",
"fedisplacementmap",
"onbefordeactivate",
"aria-busy",
"preservealpha",
"x-height",
"animateColor",
"fediffuselighting",
"seamless",
"onrepeat",
"textPath",
"aria-setsize",
"glyphRef",
"ondatasetchanged",
"displaystyle",
"specularconstant",
"contentstyletype",
"formnovalidate",
"tablevalues",
"maskcontentunits",
"onresize",
"inputmode",
"onmousemove",
"keypoints",
"equalcolumns",
"selection",
"baseProfile",
"altGlyphItem",
"clipPathUnits",
"viewtarget",
"figcaption",
"accent-height",
"font-stretch",
"font-face-uri",
"equalrows",
"startOffset",
"animateMotion",
"feMorphology",
"controls",
"contextmenu",
"ondeactivate",
"autofocus",
"flood-opacity",
"attributeType",
"numoctaves",
"aria-multiline",
"zoomAndPan",
"accumulate",
"minlength",
"quotient",
"attributename",
"textlength",
"radialGradient",
"autoplay",
"onformchange",
"superscriptshift",
"columnlines",
"color-interpolation",
"gradienttransform",
"missing-glyph",
"onrowsinserted",
"kernelMatrix",
"onpageshow",
"visibility",
"viewTarget",
"feDisplacementMap",
"aria-valuemin",
"kernelUnitLength",
"imaginary",
"cellpadding",
"formmethod",
"overline-thickness",
"onmouseup",
"onbounce",
"unselectable",
"aria-dropeffect",
"codebase",
"malignmark",
"pathLength",
"interval",
"onmessage",
"laplacian",
"xml:base",
"aria-checked",
"exponent",
"onmouseout",
"veryverythinmathspace",
"onbeforepaste",
"lengthadjust",
"patterntransform",
"determinant",
"progress",
"unicode-bidi",
"vert-adv-y",
"strikethrough-thickness",
"onmouseenter",
"multiple",
"symmetric",
"fegaussianblur",
"altglyph",
"ondragenter",
"dataformatas",
"xlink:href",
"integrity",
"itemprop",
"aria-controls",
"font-size-adjust",
"http-equiv",
"onbeforeactivate",
"startoffset",
"onafterupdate",
"basefont",
"onlanguagechange",
"rationals",
"ondatasetcomplete",
"oncellchange",
"onactivate",
"onmovestart",
"line-height",
"menclose",
"onerrorupdate",
"framespacing",
"naturalnumbers",
"notation",
"lowlimit",
"listener",
"vert-origin-x",
"repeatcount",
"aria-templateid",
"baseline",
"template",
"specularexponent",
"writing-mode",
"onfocusin",
"patternContentUnits",
"feconvolvematrix",
"mathematical",
"font-face-format",
"onrowsdelete",
"additive",
"novalidate",
"font-face",
"overline-position",
"filterres",
"matrixrow",
"mathvariant",
"onstorage",
"font-face-src",
"underline-thickness",
"feturbulence",
"optgroup",
"onscroll",
"frameborder",
"conjugate",
"gradientTransform",
"specularConstant",
"mathsize",
"markerUnits",
"complexes",
"pathlength",
"linebreak",
"glyph-name",
"unicode-range",
"baseprofile",
"required",
"readonly",
"onmouseleave",
"integers",
"marker-end",
"rowspacing",
"diffuseConstant",
"autocomplete",
"repeatdur",
"definitionURL",
"mathbackground",
"feGaussianBlur",
"amplitude",
"onmoveend",
"download",
"diffuseconstant",
"externalresourcesrequired",
"aria-autocomplete",
"keytimes",
"altglyphdef",
"codetype",
"background",
"fieldset",
"partialdiff",
"pointsatx",
"formaction",
"xlink:show",
"spreadmethod",
"filterUnits",
"nomodule",
"fontstyle",
"ondragend",
"fecolormatrix",
"itemtype",
"attributetype",
"mprescripts",
"orientation",
"keyPoints",
"maskUnits",
"pointer-events",
"valuetype",
"marginwidth",
"statechange",
"onfilterchange",
"rowlines",
"aria-describedby",
"lighting-color",
"kernelunitlength",
"equivalent",
"aria-relevant",
"stroke-linecap",
"disabled",
"patternunits",
"strikethrough-position",
"ychannelselector",
"",
"requiredfeatures",
"fePointLight",
"onforminput",
"horiz-origin-y",
"keyTimes",
"requiredextensions",
"verythickmathspace",
"primitiveUnits",
"pointsaty",
"scrolldelay",
"colgroup",
"aria-expanded",
"font-weight",
"baseFrequency",
"operator",
"animateTransform",
"transform",
"marker-mid",
"mlabeledtr",
"altglyphitem",
"markerHeight",
"fill-rule",
"ideographic",
"yChannelSelector",
"datetime",
"linethickness",
"repeat-min",
"markerWidth",
"stretchy",
"glyph-orientation-horizontal",
"exponentiale",
"edgemode",
"stroke-opacity",
"femergenode",
"aria-haspopup",
"variance",
"color-interpolation-filters",
"textarea",
"radiogroup",
"allowfullscreen",
"domainofapplication",
"feOffset",
"emptyset",
"occurrence",
"image-rendering",
"calcMode",
"patternTransform",
"onpagehide",
"feComponentTransfer",
"feDropShadow",
"intersect",
"horiz-origin-x",
"definitionurl",
"xlink:role",
"accentunder",
"stop-color",
"feMergeNode",
"xlink:title",
"fontweight",
"aria-flowto",
"fedistantlight",
"onbeforeupdate",
"v-alphabetic",
"onchange",
"stroke-miterlimit",
"divergence",
"linearGradient",
"limitingConeAngle",
"clip-path",
"scriptlevel",
"onunload",
"munderover",
"repeatCount",
"placeholder",
"vectorproduct",
"columnspan",
"underline-position",
"baseline-shift",
"aria-secret",
"referrerpolicy",
"calcmode",
"onhashchange",
"columnalign",
"marginheight",
"aria-grab",
"specification",
"formenctype",
"limitingconeangle",
"cartesianproduct",
"annotation",
"factorof",
"momentabout",
"aria-valuemax",
"lengthAdjust",
"mediummathspace",
"panose-1",
"movablelimits",
"filterunits",
"stitchTiles",
"primitiveunits",
"factorial",
"fecomponenttransfer",
"systemLanguage",
"onbeforeunload",
"noresize",
"dominant-baseline",
"animation",
"preserveAspectRatio",
"clippathunits",
"gradientUnits",
"imaginaryi",
"oncontextmenu",
"noframes",
"rowalign",
"horiz-adv-x",
"xlink:arcrole",
"pointsAtZ",
"surfaceScale",
"glyph-orientation-vertical",
"markerwidth",
"manifest",
"xlink:type",
"text-anchor",
"decoding",
"xmlns:xlink",
"noscript",
"onselect",
"xml:space",
"aria-invalid",
"transpose",
"selector",
"property",
"aria-pressed",
"definition-src",
"tableValues",
"aria-activedescendant",
"text-rendering",
"semantics",
"separators",
"scriptminsize",
"fill-opacity",
"font-variant",
"aria-labelledby",
"columnwidth",
"alignment-baseline",
"fepointlight",
"xChannelSelector",
"foreignObject",
"bevelled",
"feTurbulence",
"fespotlight",
"prsubset",
"basefrequency",
"gradientunits",
"autosubmit",
"eulergamma",
"oninvalid",
"draggable",
"polyline",
"maligngroup",
"textLength",
"onsubmit",
"mmultiscripts",
"contentScriptType",
"codomain",
"oncontrolselect",
"ondblclick",
"accesskey",
"menuitem",
"aria-channel",
"formtarget",
"onfinish",
"clippath",
"ondragleave",
"ondragdrop",
"actiontype",
"solidcolor",
"font-style",
"outerproduct",
"notanumber",
"requiredFeatures",
"onselectstart",
"ondragstart",
"alignmentscope",
"scrolling",
"onpropertychange",
"onkeypress",
"annotation-xml",
"stdDeviation",
"specularExponent",
"veryverythickmathspace",
"frameset",
"onkeydown",
"aria-multiselectable",
"fontsize",
"onmousewheel",
"onbeforecut",
"preserveAlpha",
"mathcolor",
"attributeName",
"aria-disabled",
"preserveaspectratio",
"v-ideographic",
"thinmathspace",
"onlosecapture",
"aria-posinset",
"separator",
"multicol",
"xlink:actuate",
"markerheight",
"clipPath",
"blockquote",
"aria-valuenow"] , hashes : & [2965230298u32 , 2172529172u32 , 1853897548u32 , 2488889144u32 , 4010765371u32 , 1281759160u32 , 1421968030u32 , 1578024761u32 , 2038498193u32 , 2986159298u32 , 631093780u32 , 977519882u32 , 2884343567u32 , 1137297938u32 , 1324932700u32 , 2377326793u32 , 4016453314u32 , 2250640510u32 , 3862168567u32 , 2761967137u32 , 4266430317u32 , 742460070u32 , 1528320406u32 , 1857928766u32 , 303986147u32 , 4193230156u32 , 2998850704u32 , 3819629636u32 , 3482160649u32 , 6175338u32 , 1545307082u32 , 968834697u32 , 53892155u32 , 3199297686u32 , 2980412961u32 , 1123905875u32 , 4179141959u32 , 2190494129u32 , 3668239605u32 , 4155473355u32 , 3441525639u32 , 2701977759u32 , 868058960u32 , 150393700u32 , 1099196523u32 , 940673285u32 , 130627326u32 , 1209573904u32 , 1776997671u32 , 1013909549u32 , 1026931307u32 , 427162403u32 , 4032437431u32 , 2673802232u32 , 149474300u32 , 4133853804u32 , 1437637129u32 , 3123322936u32 , 2619524090u32 , 4018656289u32 , 1492357159u32 , 1081225797u32 , 3646787135u32 , 1326112048u32 , 1285071230u32 , 2961964106u32 , 3247137570u32 , 2213967518u32 , 2062376914u32 , 1647114263u32 , 1496007974u32 , 951933631u32 , 1290517656u32 , 2620153124u32 , 297767543u32 , 938548574u32 , 441619653u32 , 2753608321u32 , 635163395u32 , 1031924441u32 , 3213189697u32 , 1008080924u32 , 3653391143u32 , 3888122413u32 , 2806594463u32 , 3855161187u32 , 2875176293u32 , 3022346510u32 , 1363498200u32 , 1713409972u32 , 1704381425u32 , 642136761u32 , 2577707114u32 , 1106010321u32 , 3574495545u32 , 3978774502u32 , 207807063u32 , 4104071589u32 , 2068298677u32 , 718491279u32 , 496466373u32 , 2607187618u32 , 3296146244u32 , 316404133u32 , 23332298u32 , 1494292933u32 , 498024801u32 , 3838527384u32 , 1511898050u32 , 3345816294u32 , 3088760233u32 , 742853127u32 , 986461641u32 , 3074935477u32 , 2791371120u32 , 3072954000u32 , 1493588833u32 , 2481108222u32 , 1846373592u32 , 3064390904u32 , 26369323u32 , 4121432320u32 , 1340310784u32 , 388019751u32 , 2660037162u32 , 3084283302u32 , 3017840974u32 , 3087731897u32 , 1638551108u32 , 1980252733u32 , 904157188u32 , 3776643770u32 , 2352150961u32 , 4114207458u32 , 4088548386u32 , 3173477931u32 , 857129565u32 , 4064199297u32 , 507264248u32 , 3717621328u32 , 3517724257u32 , 797499465u32 , 948133874u32 , 909665113u32 , 225950890u32 , 839481326u32 , 2324745345u32 , 3022377460u32 , 1443202521u32 , 328232673u32 , 67060975u32 , 2274933261u32 , 582074557u32 , 1967061597u32 , 1328456326u32 , 2005060454u32 , 2440702496u32 , 3756296120u32 , 4067843623u32 , 1693167880u32 , 4048787516u32 , 787973706u32 , 3572217425u32 , 2974337319u32 , 3648607726u32 , 1397943031u32 , 1634030783u32 , 3598404451u32 , 1882820659u32 , 3371793166u32 , 1529410224u32 , 268294220u32 , 1660726878u32 , 193163341u32 , 1754753923u32 , 2771320901u32 , 2749033157u32 , 1060911337u32 , 3038336232u32 , 610893786u32 , 33840900u32 , 3708044859u32 , 1969026116u32 , 623782337u32 , 2389793136u32 , 1622957280u32 , 2488083091u32 , 1048020461u32 , 4001891308u32 , 3505831613u32 , 688563368u32 , 251844155u32 , 3278498399u32 , 1114315443u32 , 890471348u32 , 3359083184u32 , 1704100245u32 , 1642132156u32 , 2113798551u32 , 2407704912u32 , 4197022858u32 , 3139000449u32 , 2938551886u32 , 2468381339u32 , 4214847380u32 , 697370994u32 , 1727774507u32 , 1784393862u32 , 680267915u32 , 3081310440u32 , 2091062413u32 , 1737183206u32 , 3394829011u32 , 155801033u32 , 3303937590u32 , 2901051737u32 , 1764594812u32 , 3337178u32 , 236464811u32 , 1459652027u32 , 1402964693u32 , 1918948260u32 , 4197990453u32 , 2152634883u32 , 3813953811u32 , 2526332624u32 , 3564373280u32 , 3107512368u32 , 2710653095u32 , 1700718142u32 , 1563912927u32 , 4125580644u32 , 835417400u32 , 322837783u32 , 3469436088u32 , 4114830095u32 , 3164895664u32 , 4260186941u32 , 3857554377u32 , 3978549325u32 , 745064063u32 , 2907088801u32 , 3391938715u32 , 2606928828u32 , 3473820876u32 , 1346406727u32 , 3538034810u32 , 2985998690u32 , 3290302249u32 , 3364582022u32 , 269731020u32 , 909579565u32 , 1465547969u32 , 1833541319u32 , 3023844475u32 , 1463711312u32 , 3805722218u32 , 625288246u32 , 1659529969u32 , 297387744u32 , 3561280171u32 , 2720850668u32 , 1271998022u32 , 1029483847u32 , 2825708013u32 , 2176790048u32 , 2384049441u32 , 3315223323u32 , 1196494198u32 , 2766318564u32 , 1289272887u32 , 2286720981u32 , 4144887102u32 , 2869380463u32 , 2047753258u32 , 3192182081u32 , 2458868732u32 , 3812424145u32 , 3349181976u32 , 624093324u32 , 1038372645u32 , 631162154u32 , 2351838155u32 , 3436454788u32 , 2271299428u32 , 1694998203u32 , 59658625u32 , 24676607u32 , 314533796u32 , 1980444132u32 , 1585700207u32 , 1081021937u32 , 3569972244u32 , 657335448u32 , 2817038524u32 , 3251283326u32 , 3263344947u32 , 1670804630u32 , 357019207u32 , 1313537745u32 , 1503507106u32 , 191876287u32 , 3390498297u32 , 3761485389u32 , 3041455211u32 , 1751032114u32 , 3953335784u32 , 3054316836u32 , 2146706716u32 , 1014874794u32 , 290068694u32 , 1746599759u32 , 1359418558u32 , 1639866691u32 , 1152797465u32 , 898067958u32 , 139625561u32 , 3173625849u32 , 1411183032u32 , 41264406u32 , 127349794u32 , 1004995609u32 , 722849851u32 , 3224307212u32 , 2909783456u32 , 1068196084u32 , 1695163510u32 , 1343823872u32 , 2282081120u32 , 1715378710u32 , 540984354u32 , 167018041u32 , 3140418151u32 , 2196595662u32 , 3859396703u32 , 731340681u32 , 2338483475u32 , 717168679u32 , 2731266052u32 , 89533980u32 , 4019205149u32 , 2627228424u32 , 876893636u32 , 3447324541u32 , 4279947214u32 , 1545869555u32 , 3937177209u32 , 585557574u32 , 570359024u32 , 3006277213u32 , 517970045u32 , 2075053822u32 , 990264918u32 , 1943821868u32 , 2730644329u32 , 3799256240u32 , 3434111473u32 , 487396000u32 , 3895686923u32 , 859546789u32 , 2253042667u32 , 3221036252u32 , 2055579803u32 , 1079146945u32 , 4082073077u32 , 3368766693u32 , 2112894915u32 , 872051764u32 , 1582153498u32 , 1135843586u32 , 2117758015u32 , 4274065971u32 , 235814134u32 , 2145215358u32 , 3588373335u32 , 482099679u32 , 2813597207u32 , 2754786514u32 , 214212608u32 , 671572742u32 , 1122986055u32 , 2990288183u32 , 3503166008u32 , 167340041u32 , 1160497299u32 , 3420533286u32 , 183626286u32 , 3468989663u32 , 898289440u32 , 2832170387u32 , 611308476u32 , 3231125223u32 , 3535003966u32 , 4157184116u32 , 2143535654u32 , 527925402u32 , 3522778558u32 , 2007550408u32 , 2229477384u32 , 2893479988u32 , 540156035u32 , 840604679u32 , 471362903u32 , 906623274u32 , 3553740950u32 , 1000927536u32 , 1406178112u32 , 3608092222u32 , 4123782853u32 , 3725445512u32 , 1713731671u32 , 2555432291u32 , 3730423347u32 , 1003700867u32 , 3543883658u32 , 1270424231u32 , 960352296u32 , 790236809u32 , 2705478669u32 , 1039356818u32 , 1531081973u32 , 11851321u32 , 115637463u32 , 1436742140u32 , 1462083347u32 , 597485693u32 , 1064789251u32 , 2192643058u32 , 671909442u32 , 1597870884u32 , 3590479039u32 , 1956396338u32 , 175932733u32 , 3745411061u32 , 261387879u32 , 1270941540u32 , 1108361085u32 , 4004244404u32 , 4227008938u32 , 2271863710u32 , 1113648994u32 , 3161172129u32 , 1372447217u32 , 3391050331u32 , 326494834u32 , 1924991364u32 , 3316294029u32 , 95564789u32 , 1341662051u32 , 1733779952u32 , 2052860768u32 , 510569370u32 , 4086049772u32 , 3650491062u32 , 1960989138u32 , 340120193u32 , 3467076559u32 , 2627759492u32 , 3766194097u32 , 1867791235u32 , 798432117u32 , 1490652026u32 , 3520680811u32 , 1906307066u32 , 660920321u32 , 3182531199u32 , 1390052554u32 , 2994067376u32 , 1327970202u32 , 3709793219u32 , 3173400481u32 , 1344436999u32 , 4080036470u32 , 2648768565u32 , 1089255496u32 , 3225919750u32 , 1031843807u32 , 1622799733u32 , 11175636u32 , 3480491085u32 , 1674115585u32 , 2162760400u32 , 3311581079u32 , 3720179864u32 , 1961077096u32 , 2927217335u32 , 3629415669u32 , 1747256964u32 , 4208387215u32 , 3949169397u32 , 3496513502u32 , 1904259492u32 , 544785510u32 , 3472575083u32 , 2231254690u32 , 1151125310u32 , 551601771u32 , 962337312u32 , 1425318046u32 , 836037603u32 , 3356497412u32 , 1466152561u32 , 189988860u32 , 2509936390u32 , 776053845u32 , 2519983718u32 , 3735993810u32 , 1696233065u32 , 3931194778u32 , 3727352922u32 , 3031015162u32 , 738404731u32 , 917510687u32 , 42180808u32 , 2410855252u32 , 1469020628u32 , 1932517039u32 , 4219469015u32 , 2770211272u32 , 3174660173u32 , 2798901808u32 , 849353939u32 , 1213081654u32 , 2537656704u32 , 4287406983u32 , 3015830598u32 , 2591735095u32 , 3595420161u32 , 4019313672u32 , 3848540887u32 , 1085252765u32 , 3318082606u32 , 1340705914u32 , 3273539984u32 , 4257931290u32 , 1265818470u32 , 1500515759u32 , 1365633322u32 , 1356155463u32 , 4174481087u32 , 3674106722u32 , 3200099719u32 , 1109713652u32 , 4151148662u32 , 3471797576u32 , 1784113404u32 , 3044528111u32 , 2331857769u32 , 428550904u32 , 372536167u32 , 1268358839u32 , 491646294u32 , 1040004347u32 , 577943792u32 , 3353385752u32 , 1964478094u32 , 2822081788u32 , 3792741489u32 , 882636649u32 , 2140727092u32 , 825000363u32 , 2363338602u32 , 1054325476u32 , 1159398089u32 , 3148690945u32 , 520649620u32 , 13967138u32 , 143478574u32 , 3172500845u32 , 3937191824u32 , 1086403303u32 , 259654618u32 , 284653812u32 , 497920229u32 , 3367662520u32 , 4110733451u32 , 1289880550u32 , 3377397637u32] } ;
& SET } fn empty_string_index () -> u32 { 364u32 } } pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_63_6F_6E_74_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (0u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_6F_72_74 : LocalName = LocalName :: pack_static (1u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_72_69_6E_74 : LocalName = LocalName :: pack_static (2u32) ;
pub const ATOM_LOCALNAME__74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (3u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_66_6C_6F_77 : LocalName = LocalName :: pack_static (4u32) ;
pub const ATOM_LOCALNAME__66_65_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (5u32) ;
pub const ATOM_LOCALNAME__68_69_64_65_66_6F_63_75_73 : LocalName = LocalName :: pack_static (6u32) ;
pub const ATOM_LOCALNAME__73_74_69_74_63_68_74_69_6C_65_73 : LocalName = LocalName :: pack_static (7u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_61_6C_67_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (8u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_61_76_61_69_6C_61_62_6C_65 : LocalName = LocalName :: pack_static (9u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_44_65_66 : LocalName = LocalName :: pack_static (10u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_65_64_69_74_66_6F_63_75_73 : LocalName = LocalName :: pack_static (11u32) ;
pub const ATOM_LOCALNAME__70_72_65_66_65_74_63_68 : LocalName = LocalName :: pack_static (12u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_79 : LocalName = LocalName :: pack_static (13u32) ;
pub const ATOM_LOCALNAME__73_75_62_73_63_72_69_70_74_73_68_69_66_74 : LocalName = LocalName :: pack_static (14u32) ;
pub const ATOM_LOCALNAME__73_74_6F_70_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (15u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_68_69_64_64_65_6E : LocalName = LocalName :: pack_static (16u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_63_65_70_74 : LocalName = LocalName :: pack_static (17u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (18u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_43_6F_6E_74_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (19u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_6F_75_74 : LocalName = LocalName :: pack_static (20u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_61_78 : LocalName = LocalName :: pack_static (21u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6F_77_6E_73 : LocalName = LocalName :: pack_static (22u32) ;
pub const ATOM_LOCALNAME__65_6E_63_6F_64_69_6E_67 : LocalName = LocalName :: pack_static (23u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (24u32) ;
pub const ATOM_LOCALNAME__73_75_72_66_61_63_65_73_63_61_6C_65 : LocalName = LocalName :: pack_static (25u32) ;
pub const ATOM_LOCALNAME__69_72_72_65_6C_65_76_61_6E_74 : LocalName = LocalName :: pack_static (26u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6C_6F_72_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (27u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68 : LocalName = LocalName :: pack_static (28u32) ;
pub const ATOM_LOCALNAME__73_79_73_74_65_6D_6C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (29u32) ;
pub const ATOM_LOCALNAME__6F_6E_6F_6E_6C_69_6E_65 : LocalName = LocalName :: pack_static (30u32) ;
pub const ATOM_LOCALNAME__66_65_73_70_65_63_75_6C_61_72_6C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (31u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_72_65_66 : LocalName = LocalName :: pack_static (32u32) ;
pub const ATOM_LOCALNAME__61_72_61_62_69_63_2D_66_6F_72_6D : LocalName = LocalName :: pack_static (33u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_7A : LocalName = LocalName :: pack_static (34u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (35u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_2D_72_75_6C_65 : LocalName = LocalName :: pack_static (36u32) ;
pub const ATOM_LOCALNAME__6E_75_6D_4F_63_74_61_76_65_73 : LocalName = LocalName :: pack_static (37u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_70_74_2D_63_68_61_72_73_65_74 : LocalName = LocalName :: pack_static (38u32) ;
pub const ATOM_LOCALNAME__6D_61_78_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (39u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_74_6F_6D_69_63 : LocalName = LocalName :: pack_static (40u32) ;
pub const ATOM_LOCALNAME__66_65_53_70_6F_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (41u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (42u32) ;
pub const ATOM_LOCALNAME__73_70_72_65_61_64_4D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (43u32) ;
pub const ATOM_LOCALNAME__63_72_6F_73_73_6F_72_69_67_69_6E : LocalName = LocalName :: pack_static (44u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_6C_65_63_74_65_64 : LocalName = LocalName :: pack_static (45u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_65_78_69_74 : LocalName = LocalName :: pack_static (46u32) ;
pub const ATOM_LOCALNAME__63_61_70_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (47u32) ;
pub const ATOM_LOCALNAME__66_65_64_72_6F_70_73_68_61_64_6F_77 : LocalName = LocalName :: pack_static (48u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (49u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_65_64_69_74_61_62_6C_65 : LocalName = LocalName :: pack_static (50u32) ;
pub const ATOM_LOCALNAME__65_64_67_65_4D_6F_64_65 : LocalName = LocalName :: pack_static (51u32) ;
pub const ATOM_LOCALNAME__76_2D_6D_61_74_68_65_6D_61_74_69_63_61_6C : LocalName = LocalName :: pack_static (52u32) ;
pub const ATOM_LOCALNAME__75_6E_69_74_73_2D_70_65_72_2D_65_6D : LocalName = LocalName :: pack_static (53u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_73_69_74_65 : LocalName = LocalName :: pack_static (54u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_66_61_6D_69_6C_79 : LocalName = LocalName :: pack_static (55u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_73_63_6F_70_65 : LocalName = LocalName :: pack_static (56u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (57u32) ;
pub const ATOM_LOCALNAME__68_72_65_66_6C_61_6E_67 : LocalName = LocalName :: pack_static (58u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_63_72_69_70_74_74_79_70_65 : LocalName = LocalName :: pack_static (59u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_64_69_74_69_6F_6E : LocalName = LocalName :: pack_static (60u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_61_64_79_73_74_61_74_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (61u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_70_72_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (62u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_65_64 : LocalName = LocalName :: pack_static (63u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_65_6E_74_65_72 : LocalName = LocalName :: pack_static (64u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_77_69_64_74_68 : LocalName = LocalName :: pack_static (65u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_73_74_61_72_74 : LocalName = LocalName :: pack_static (66u32) ;
pub const ATOM_LOCALNAME__6D_70_68_61_6E_74_6F_6D : LocalName = LocalName :: pack_static (67u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_76_65_72 : LocalName = LocalName :: pack_static (68u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_6C_61_74_65 : LocalName = LocalName :: pack_static (69u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_74_79_6C_65_54_79_70_65 : LocalName = LocalName :: pack_static (70u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_6E_61_6D_65 : LocalName = LocalName :: pack_static (71u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_70_61_74_68 : LocalName = LocalName :: pack_static (72u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_6F_76_65_72 : LocalName = LocalName :: pack_static (73u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_45_78_74_65_6E_73_69_6F_6E_73 : LocalName = LocalName :: pack_static (74u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65 : LocalName = LocalName :: pack_static (75u32) ;
pub const ATOM_LOCALNAME__74_61_62_69_6E_64_65_78 : LocalName = LocalName :: pack_static (76u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_61_64_6F_6E_6C_79 : LocalName = LocalName :: pack_static (77u32) ;
pub const ATOM_LOCALNAME__61_6C_70_68_61_62_65_74_69_63 : LocalName = LocalName :: pack_static (78u32) ;
pub const ATOM_LOCALNAME__66_65_6D_6F_72_70_68_6F_6C_6F_67_79 : LocalName = LocalName :: pack_static (79u32) ;
pub const ATOM_LOCALNAME__6C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (80u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_75_6E_69_74_73 : LocalName = LocalName :: pack_static (81u32) ;
pub const ATOM_LOCALNAME__7A_6F_6F_6D_61_6E_64_70_61_6E : LocalName = LocalName :: pack_static (82u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_6C_65_72_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (83u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_61_72_72_61_79 : LocalName = LocalName :: pack_static (84u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_6F_70_79 : LocalName = LocalName :: pack_static (85u32) ;
pub const ATOM_LOCALNAME__66_65_53_70_65_63_75_6C_61_72_4C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (86u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_73_74_61_6E_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (87u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (88u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_58 : LocalName = LocalName :: pack_static (89u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_73_74_61_72_74 : LocalName = LocalName :: pack_static (90u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_44_75_72 : LocalName = LocalName :: pack_static (91u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_6C_61_6E_67 : LocalName = LocalName :: pack_static (92u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_73_69_7A_65_6D_75_6C_74_69_70_6C_69_65_72 : LocalName = LocalName :: pack_static (93u32) ;
pub const ATOM_LOCALNAME__76_2D_68_61_6E_67_69_6E_67 : LocalName = LocalName :: pack_static (94u32) ;
pub const ATOM_LOCALNAME__67_72_6F_75_70_61_6C_69_67_6E : LocalName = LocalName :: pack_static (95u32) ;
pub const ATOM_LOCALNAME__73_68_61_70_65_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (96u32) ;
pub const ATOM_LOCALNAME__6C_65_74_74_65_72_2D_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (97u32) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_64_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (98u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_55_6E_69_74_73 : LocalName = LocalName :: pack_static (99u32) ;
pub const ATOM_LOCALNAME__6F_6E_6F_66_66_6C_69_6E_65 : LocalName = LocalName :: pack_static (100u32) ;
pub const ATOM_LOCALNAME__6B_65_79_53_70_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (101u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6E_76_6F_6C_76_65_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (102u32) ;
pub const ATOM_LOCALNAME__64_69_72_65_63_74_69_6F_6E : LocalName = LocalName :: pack_static (103u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_52_65_73 : LocalName = LocalName :: pack_static (104u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_64_6F_77_6E : LocalName = LocalName :: pack_static (105u32) ;
pub const ATOM_LOCALNAME__70_6C_61_69_6E_74_65_78_74 : LocalName = LocalName :: pack_static (106u32) ;
pub const ATOM_LOCALNAME__6C_6F_6E_67_64_65_73_63 : LocalName = LocalName :: pack_static (107u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (108u32) ;
pub const ATOM_LOCALNAME__73_70_65_6C_6C_63_68_65_63_6B : LocalName = LocalName :: pack_static (109u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_66_74_65_72_70_72_69_6E_74 : LocalName = LocalName :: pack_static (110u32) ;
pub const ATOM_LOCALNAME__73_63_61_6C_61_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (111u32) ;
pub const ATOM_LOCALNAME__72_65_6E_64_65_72_69_6E_67_2D_69_6E_74_65_6E_74 : LocalName = LocalName :: pack_static (112u32) ;
pub const ATOM_LOCALNAME__65_6C_65_76_61_74_69_6F_6E : LocalName = LocalName :: pack_static (113u32) ;
pub const ATOM_LOCALNAME__73_74_64_64_65_76_69_61_74_69_6F_6E : LocalName = LocalName :: pack_static (114u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (115u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (116u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_6C_69_73_74 : LocalName = LocalName :: pack_static (117u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_6D_6F_74_69_6F_6E : LocalName = LocalName :: pack_static (118u32) ;
pub const ATOM_LOCALNAME__69_6E_66_69_6E_69_74_79 : LocalName = LocalName :: pack_static (119u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_69_76_65 : LocalName = LocalName :: pack_static (120u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_6D_69_6C_79 : LocalName = LocalName :: pack_static (121u32) ;
pub const ATOM_LOCALNAME__70_69_65_63_65_77_69_73_65 : LocalName = LocalName :: pack_static (122u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_6A_6F_69_6E : LocalName = LocalName :: pack_static (123u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_73_69_74_65 : LocalName = LocalName :: pack_static (124u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_61_74_61_74_79_70_65 : LocalName = LocalName :: pack_static (125u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_66_66_75_73_65_4C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (126u32) ;
pub const ATOM_LOCALNAME__63_65_6C_6C_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (127u32) ;
pub const ATOM_LOCALNAME__6D_65_74_61_64_61_74_61 : LocalName = LocalName :: pack_static (128u32) ;
pub const ATOM_LOCALNAME__6B_65_79_73_70_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (129u32) ;
pub const ATOM_LOCALNAME__66_6F_72_65_69_67_6E_6F_62_6A_65_63_74 : LocalName = LocalName :: pack_static (130u32) ;
pub const ATOM_LOCALNAME__72_65_76_65_72_73_65_64 : LocalName = LocalName :: pack_static (131u32) ;
pub const ATOM_LOCALNAME__6D_6F_7A_62_72_6F_77_73_65_72 : LocalName = LocalName :: pack_static (132u32) ;
pub const ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_52_65_73_6F_75_72_63_65_73_52_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (133u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (134u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_75_6E_69_74_73 : LocalName = LocalName :: pack_static (135u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_64_65_63_6F_72_61_74_69_6F_6E : LocalName = LocalName :: pack_static (136u32) ;
pub const ATOM_LOCALNAME__78_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (137u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_6F_70_73_74_61_74_65 : LocalName = LocalName :: pack_static (138u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_59 : LocalName = LocalName :: pack_static (139u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (140u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_61_72_67_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (141u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_65_76_65_6C : LocalName = LocalName :: pack_static (142u32) ;
pub const ATOM_LOCALNAME__6F_74_68_65_72_77_69_73_65 : LocalName = LocalName :: pack_static (143u32) ;
pub const ATOM_LOCALNAME__77_6F_72_64_2D_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (144u32) ;
pub const ATOM_LOCALNAME__65_6E_61_62_6C_65_2D_62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (145u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_73_70_6C_61_63_65_6D_65_6E_74_6D_61_70 : LocalName = LocalName :: pack_static (146u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_64_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (147u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_62_75_73_79 : LocalName = LocalName :: pack_static (148u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_6C_70_68_61 : LocalName = LocalName :: pack_static (149u32) ;
pub const ATOM_LOCALNAME__78_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (150u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_43_6F_6C_6F_72 : LocalName = LocalName :: pack_static (151u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_66_66_75_73_65_6C_69_67_68_74_69_6E_67 : LocalName = LocalName :: pack_static (152u32) ;
pub const ATOM_LOCALNAME__73_65_61_6D_6C_65_73_73 : LocalName = LocalName :: pack_static (153u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_70_65_61_74 : LocalName = LocalName :: pack_static (154u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_50_61_74_68 : LocalName = LocalName :: pack_static (155u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_74_73_69_7A_65 : LocalName = LocalName :: pack_static (156u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_52_65_66 : LocalName = LocalName :: pack_static (157u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_68_61_6E_67_65_64 : LocalName = LocalName :: pack_static (158u32) ;
pub const ATOM_LOCALNAME__64_69_73_70_6C_61_79_73_74_79_6C_65 : LocalName = LocalName :: pack_static (159u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_63_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (160u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_74_79_6C_65_74_79_70_65 : LocalName = LocalName :: pack_static (161u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_6E_6F_76_61_6C_69_64_61_74_65 : LocalName = LocalName :: pack_static (162u32) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65_76_61_6C_75_65_73 : LocalName = LocalName :: pack_static (163u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_63_6F_6E_74_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (164u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_73_69_7A_65 : LocalName = LocalName :: pack_static (165u32) ;
pub const ATOM_LOCALNAME__69_6E_70_75_74_6D_6F_64_65 : LocalName = LocalName :: pack_static (166u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6D_6F_76_65 : LocalName = LocalName :: pack_static (167u32) ;
pub const ATOM_LOCALNAME__6B_65_79_70_6F_69_6E_74_73 : LocalName = LocalName :: pack_static (168u32) ;
pub const ATOM_LOCALNAME__65_71_75_61_6C_63_6F_6C_75_6D_6E_73 : LocalName = LocalName :: pack_static (169u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_69_6F_6E : LocalName = LocalName :: pack_static (170u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_50_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (171u32) ;
pub const ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_49_74_65_6D : LocalName = LocalName :: pack_static (172u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_50_61_74_68_55_6E_69_74_73 : LocalName = LocalName :: pack_static (173u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_74_61_72_67_65_74 : LocalName = LocalName :: pack_static (174u32) ;
pub const ATOM_LOCALNAME__66_69_67_63_61_70_74_69_6F_6E : LocalName = LocalName :: pack_static (175u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (176u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_72_65_74_63_68 : LocalName = LocalName :: pack_static (177u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_75_72_69 : LocalName = LocalName :: pack_static (178u32) ;
pub const ATOM_LOCALNAME__65_71_75_61_6C_72_6F_77_73 : LocalName = LocalName :: pack_static (179u32) ;
pub const ATOM_LOCALNAME__73_74_61_72_74_4F_66_66_73_65_74 : LocalName = LocalName :: pack_static (180u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_4D_6F_74_69_6F_6E : LocalName = LocalName :: pack_static (181u32) ;
pub const ATOM_LOCALNAME__66_65_4D_6F_72_70_68_6F_6C_6F_67_79 : LocalName = LocalName :: pack_static (182u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_73 : LocalName = LocalName :: pack_static (183u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_78_74_6D_65_6E_75 : LocalName = LocalName :: pack_static (184u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (185u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_66_6F_63_75_73 : LocalName = LocalName :: pack_static (186u32) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_64_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (187u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_54_79_70_65 : LocalName = LocalName :: pack_static (188u32) ;
pub const ATOM_LOCALNAME__6E_75_6D_6F_63_74_61_76_65_73 : LocalName = LocalName :: pack_static (189u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_6C_69_6E_65 : LocalName = LocalName :: pack_static (190u32) ;
pub const ATOM_LOCALNAME__7A_6F_6F_6D_41_6E_64_50_61_6E : LocalName = LocalName :: pack_static (191u32) ;
pub const ATOM_LOCALNAME__61_63_63_75_6D_75_6C_61_74_65 : LocalName = LocalName :: pack_static (192u32) ;
pub const ATOM_LOCALNAME__6D_69_6E_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (193u32) ;
pub const ATOM_LOCALNAME__71_75_6F_74_69_65_6E_74 : LocalName = LocalName :: pack_static (194u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_6E_61_6D_65 : LocalName = LocalName :: pack_static (195u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (196u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_61_6C_47_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (197u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_70_6C_61_79 : LocalName = LocalName :: pack_static (198u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_72_6D_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (199u32) ;
pub const ATOM_LOCALNAME__73_75_70_65_72_73_63_72_69_70_74_73_68_69_66_74 : LocalName = LocalName :: pack_static (200u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (201u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E : LocalName = LocalName :: pack_static (202u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (203u32) ;
pub const ATOM_LOCALNAME__6D_69_73_73_69_6E_67_2D_67_6C_79_70_68 : LocalName = LocalName :: pack_static (204u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_73_69_6E_73_65_72_74_65_64 : LocalName = LocalName :: pack_static (205u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_4D_61_74_72_69_78 : LocalName = LocalName :: pack_static (206u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_67_65_73_68_6F_77 : LocalName = LocalName :: pack_static (207u32) ;
pub const ATOM_LOCALNAME__76_69_73_69_62_69_6C_69_74_79 : LocalName = LocalName :: pack_static (208u32) ;
pub const ATOM_LOCALNAME__76_69_65_77_54_61_72_67_65_74 : LocalName = LocalName :: pack_static (209u32) ;
pub const ATOM_LOCALNAME__66_65_44_69_73_70_6C_61_63_65_6D_65_6E_74_4D_61_70 : LocalName = LocalName :: pack_static (210u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_69_6E : LocalName = LocalName :: pack_static (211u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_55_6E_69_74_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (212u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79 : LocalName = LocalName :: pack_static (213u32) ;
pub const ATOM_LOCALNAME__63_65_6C_6C_70_61_64_64_69_6E_67 : LocalName = LocalName :: pack_static (214u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_6D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (215u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (216u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_75_70 : LocalName = LocalName :: pack_static (217u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_6F_75_6E_63_65 : LocalName = LocalName :: pack_static (218u32) ;
pub const ATOM_LOCALNAME__75_6E_73_65_6C_65_63_74_61_62_6C_65 : LocalName = LocalName :: pack_static (219u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_72_6F_70_65_66_66_65_63_74 : LocalName = LocalName :: pack_static (220u32) ;
pub const ATOM_LOCALNAME__63_6F_64_65_62_61_73_65 : LocalName = LocalName :: pack_static (221u32) ;
pub const ATOM_LOCALNAME__6D_61_6C_69_67_6E_6D_61_72_6B : LocalName = LocalName :: pack_static (222u32) ;
pub const ATOM_LOCALNAME__70_61_74_68_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (223u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_76_61_6C : LocalName = LocalName :: pack_static (224u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_65_73_73_61_67_65 : LocalName = LocalName :: pack_static (225u32) ;
pub const ATOM_LOCALNAME__6C_61_70_6C_61_63_69_61_6E : LocalName = LocalName :: pack_static (226u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_62_61_73_65 : LocalName = LocalName :: pack_static (227u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_68_65_63_6B_65_64 : LocalName = LocalName :: pack_static (228u32) ;
pub const ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (229u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_75_74 : LocalName = LocalName :: pack_static (230u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (231u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_61_73_74_65 : LocalName = LocalName :: pack_static (232u32) ;
pub const ATOM_LOCALNAME__6C_65_6E_67_74_68_61_64_6A_75_73_74 : LocalName = LocalName :: pack_static (233u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (234u32) ;
pub const ATOM_LOCALNAME__64_65_74_65_72_6D_69_6E_61_6E_74 : LocalName = LocalName :: pack_static (235u32) ;
pub const ATOM_LOCALNAME__70_72_6F_67_72_65_73_73 : LocalName = LocalName :: pack_static (236u32) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_62_69_64_69 : LocalName = LocalName :: pack_static (237u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_61_64_76_2D_79 : LocalName = LocalName :: pack_static (238u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (239u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_65_6E_74_65_72 : LocalName = LocalName :: pack_static (240u32) ;
pub const ATOM_LOCALNAME__6D_75_6C_74_69_70_6C_65 : LocalName = LocalName :: pack_static (241u32) ;
pub const ATOM_LOCALNAME__73_79_6D_6D_65_74_72_69_63 : LocalName = LocalName :: pack_static (242u32) ;
pub const ATOM_LOCALNAME__66_65_67_61_75_73_73_69_61_6E_62_6C_75_72 : LocalName = LocalName :: pack_static (243u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68 : LocalName = LocalName :: pack_static (244u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_74_65_72 : LocalName = LocalName :: pack_static (245u32) ;
pub const ATOM_LOCALNAME__64_61_74_61_66_6F_72_6D_61_74_61_73 : LocalName = LocalName :: pack_static (246u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_68_72_65_66 : LocalName = LocalName :: pack_static (247u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_67_72_69_74_79 : LocalName = LocalName :: pack_static (248u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_70_72_6F_70 : LocalName = LocalName :: pack_static (249u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_6F_6E_74_72_6F_6C_73 : LocalName = LocalName :: pack_static (250u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65_2D_61_64_6A_75_73_74 : LocalName = LocalName :: pack_static (251u32) ;
pub const ATOM_LOCALNAME__68_74_74_70_2D_65_71_75_69_76 : LocalName = LocalName :: pack_static (252u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (253u32) ;
pub const ATOM_LOCALNAME__73_74_61_72_74_6F_66_66_73_65_74 : LocalName = LocalName :: pack_static (254u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_66_74_65_72_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (255u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_66_6F_6E_74 : LocalName = LocalName :: pack_static (256u32) ;
pub const ATOM_LOCALNAME__6F_6E_6C_61_6E_67_75_61_67_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (257u32) ;
pub const ATOM_LOCALNAME__72_61_74_69_6F_6E_61_6C_73 : LocalName = LocalName :: pack_static (258u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (259u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_65_6C_6C_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (260u32) ;
pub const ATOM_LOCALNAME__6F_6E_61_63_74_69_76_61_74_65 : LocalName = LocalName :: pack_static (261u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65_73_74_61_72_74 : LocalName = LocalName :: pack_static (262u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_2D_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (263u32) ;
pub const ATOM_LOCALNAME__6D_65_6E_63_6C_6F_73_65 : LocalName = LocalName :: pack_static (264u32) ;
pub const ATOM_LOCALNAME__6F_6E_65_72_72_6F_72_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (265u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (266u32) ;
pub const ATOM_LOCALNAME__6E_61_74_75_72_61_6C_6E_75_6D_62_65_72_73 : LocalName = LocalName :: pack_static (267u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (268u32) ;
pub const ATOM_LOCALNAME__6C_6F_77_6C_69_6D_69_74 : LocalName = LocalName :: pack_static (269u32) ;
pub const ATOM_LOCALNAME__6C_69_73_74_65_6E_65_72 : LocalName = LocalName :: pack_static (270u32) ;
pub const ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_78 : LocalName = LocalName :: pack_static (271u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_63_6F_75_6E_74 : LocalName = LocalName :: pack_static (272u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_74_65_6D_70_6C_61_74_65_69_64 : LocalName = LocalName :: pack_static (273u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (274u32) ;
pub const ATOM_LOCALNAME__74_65_6D_70_6C_61_74_65 : LocalName = LocalName :: pack_static (275u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_65_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (276u32) ;
pub const ATOM_LOCALNAME__77_72_69_74_69_6E_67_2D_6D_6F_64_65 : LocalName = LocalName :: pack_static (277u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_69_6E : LocalName = LocalName :: pack_static (278u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_43_6F_6E_74_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (279u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6E_76_6F_6C_76_65_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (280u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_65_6D_61_74_69_63_61_6C : LocalName = LocalName :: pack_static (281u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_66_6F_72_6D_61_74 : LocalName = LocalName :: pack_static (282u32) ;
pub const ATOM_LOCALNAME__6F_6E_72_6F_77_73_64_65_6C_65_74_65 : LocalName = LocalName :: pack_static (283u32) ;
pub const ATOM_LOCALNAME__61_64_64_69_74_69_76_65 : LocalName = LocalName :: pack_static (284u32) ;
pub const ATOM_LOCALNAME__6E_6F_76_61_6C_69_64_61_74_65 : LocalName = LocalName :: pack_static (285u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65 : LocalName = LocalName :: pack_static (286u32) ;
pub const ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (287u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_72_65_73 : LocalName = LocalName :: pack_static (288u32) ;
pub const ATOM_LOCALNAME__6D_61_74_72_69_78_72_6F_77 : LocalName = LocalName :: pack_static (289u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_76_61_72_69_61_6E_74 : LocalName = LocalName :: pack_static (290u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_6F_72_61_67_65 : LocalName = LocalName :: pack_static (291u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_73_72_63 : LocalName = LocalName :: pack_static (292u32) ;
pub const ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (293u32) ;
pub const ATOM_LOCALNAME__66_65_74_75_72_62_75_6C_65_6E_63_65 : LocalName = LocalName :: pack_static (294u32) ;
pub const ATOM_LOCALNAME__6F_70_74_67_72_6F_75_70 : LocalName = LocalName :: pack_static (295u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_63_72_6F_6C_6C : LocalName = LocalName :: pack_static (296u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_62_6F_72_64_65_72 : LocalName = LocalName :: pack_static (297u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_6A_75_67_61_74_65 : LocalName = LocalName :: pack_static (298u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (299u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_43_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (300u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_73_69_7A_65 : LocalName = LocalName :: pack_static (301u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_55_6E_69_74_73 : LocalName = LocalName :: pack_static (302u32) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_6C_65_78_65_73 : LocalName = LocalName :: pack_static (303u32) ;
pub const ATOM_LOCALNAME__70_61_74_68_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (304u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_62_72_65_61_6B : LocalName = LocalName :: pack_static (305u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6E_61_6D_65 : LocalName = LocalName :: pack_static (306u32) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_72_61_6E_67_65 : LocalName = LocalName :: pack_static (307u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_static (308u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (309u32) ;
pub const ATOM_LOCALNAME__72_65_61_64_6F_6E_6C_79 : LocalName = LocalName :: pack_static (310u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6C_65_61_76_65 : LocalName = LocalName :: pack_static (311u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_67_65_72_73 : LocalName = LocalName :: pack_static (312u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_65_6E_64 : LocalName = LocalName :: pack_static (313u32) ;
pub const ATOM_LOCALNAME__72_6F_77_73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_static (314u32) ;
pub const ATOM_LOCALNAME__64_69_66_66_75_73_65_43_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (315u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (316u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_64_75_72 : LocalName = LocalName :: pack_static (317u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_55_52_4C : LocalName = LocalName :: pack_static (318u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (319u32) ;
pub const ATOM_LOCALNAME__66_65_47_61_75_73_73_69_61_6E_42_6C_75_72 : LocalName = LocalName :: pack_static (320u32) ;
pub const ATOM_LOCALNAME__61_6D_70_6C_69_74_75_64_65 : LocalName = LocalName :: pack_static (321u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65_65_6E_64 : LocalName = LocalName :: pack_static (322u32) ;
pub const ATOM_LOCALNAME__64_6F_77_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (323u32) ;
pub const ATOM_LOCALNAME__64_69_66_66_75_73_65_63_6F_6E_73_74_61_6E_74 : LocalName = LocalName :: pack_static (324u32) ;
pub const ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_72_65_73_6F_75_72_63_65_73_72_65_71_75_69_72_65_64 : LocalName = LocalName :: pack_static (325u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_75_74_6F_63_6F_6D_70_6C_65_74_65 : LocalName = LocalName :: pack_static (326u32) ;
pub const ATOM_LOCALNAME__6B_65_79_74_69_6D_65_73 : LocalName = LocalName :: pack_static (327u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_64_65_66 : LocalName = LocalName :: pack_static (328u32) ;
pub const ATOM_LOCALNAME__63_6F_64_65_74_79_70_65 : LocalName = LocalName :: pack_static (329u32) ;
pub const ATOM_LOCALNAME__62_61_63_6B_67_72_6F_75_6E_64 : LocalName = LocalName :: pack_static (330u32) ;
pub const ATOM_LOCALNAME__66_69_65_6C_64_73_65_74 : LocalName = LocalName :: pack_static (331u32) ;
pub const ATOM_LOCALNAME__70_61_72_74_69_61_6C_64_69_66_66 : LocalName = LocalName :: pack_static (332u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_78 : LocalName = LocalName :: pack_static (333u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_61_63_74_69_6F_6E : LocalName = LocalName :: pack_static (334u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_73_68_6F_77 : LocalName = LocalName :: pack_static (335u32) ;
pub const ATOM_LOCALNAME__73_70_72_65_61_64_6D_65_74_68_6F_64 : LocalName = LocalName :: pack_static (336u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_55_6E_69_74_73 : LocalName = LocalName :: pack_static (337u32) ;
pub const ATOM_LOCALNAME__6E_6F_6D_6F_64_75_6C_65 : LocalName = LocalName :: pack_static (338u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_73_74_79_6C_65 : LocalName = LocalName :: pack_static (339u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_64 : LocalName = LocalName :: pack_static (340u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6C_6F_72_6D_61_74_72_69_78 : LocalName = LocalName :: pack_static (341u32) ;
pub const ATOM_LOCALNAME__69_74_65_6D_74_79_70_65 : LocalName = LocalName :: pack_static (342u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_74_79_70_65 : LocalName = LocalName :: pack_static (343u32) ;
pub const ATOM_LOCALNAME__6D_70_72_65_73_63_72_69_70_74_73 : LocalName = LocalName :: pack_static (344u32) ;
pub const ATOM_LOCALNAME__6F_72_69_65_6E_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (345u32) ;
pub const ATOM_LOCALNAME__6B_65_79_50_6F_69_6E_74_73 : LocalName = LocalName :: pack_static (346u32) ;
pub const ATOM_LOCALNAME__6D_61_73_6B_55_6E_69_74_73 : LocalName = LocalName :: pack_static (347u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_65_72_2D_65_76_65_6E_74_73 : LocalName = LocalName :: pack_static (348u32) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65_74_79_70_65 : LocalName = LocalName :: pack_static (349u32) ;
pub const ATOM_LOCALNAME__6D_61_72_67_69_6E_77_69_64_74_68 : LocalName = LocalName :: pack_static (350u32) ;
pub const ATOM_LOCALNAME__73_74_61_74_65_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (351u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_69_6C_74_65_72_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (352u32) ;
pub const ATOM_LOCALNAME__72_6F_77_6C_69_6E_65_73 : LocalName = LocalName :: pack_static (353u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_65_73_63_72_69_62_65_64_62_79 : LocalName = LocalName :: pack_static (354u32) ;
pub const ATOM_LOCALNAME__6C_69_67_68_74_69_6E_67_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (355u32) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_65_6C_75_6E_69_74_6C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (356u32) ;
pub const ATOM_LOCALNAME__65_71_75_69_76_61_6C_65_6E_74 : LocalName = LocalName :: pack_static (357u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_72_65_6C_65_76_61_6E_74 : LocalName = LocalName :: pack_static (358u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_63_61_70 : LocalName = LocalName :: pack_static (359u32) ;
pub const ATOM_LOCALNAME__64_69_73_61_62_6C_65_64 : LocalName = LocalName :: pack_static (360u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_75_6E_69_74_73 : LocalName = LocalName :: pack_static (361u32) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (362u32) ;
pub const ATOM_LOCALNAME__79_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (363u32) ;
pub const ATOM_LOCALNAME_ : LocalName = LocalName :: pack_static (364u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_66_65_61_74_75_72_65_73 : LocalName = LocalName :: pack_static (365u32) ;
pub const ATOM_LOCALNAME__66_65_50_6F_69_6E_74_4C_69_67_68_74 : LocalName = LocalName :: pack_static (366u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_72_6D_69_6E_70_75_74 : LocalName = LocalName :: pack_static (367u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_79 : LocalName = LocalName :: pack_static (368u32) ;
pub const ATOM_LOCALNAME__6B_65_79_54_69_6D_65_73 : LocalName = LocalName :: pack_static (369u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_65_78_74_65_6E_73_69_6F_6E_73 : LocalName = LocalName :: pack_static (370u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (371u32) ;
pub const ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_55_6E_69_74_73 : LocalName = LocalName :: pack_static (372u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_79 : LocalName = LocalName :: pack_static (373u32) ;
pub const ATOM_LOCALNAME__73_63_72_6F_6C_6C_64_65_6C_61_79 : LocalName = LocalName :: pack_static (374u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_67_72_6F_75_70 : LocalName = LocalName :: pack_static (375u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_65_78_70_61_6E_64_65_64 : LocalName = LocalName :: pack_static (376u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_77_65_69_67_68_74 : LocalName = LocalName :: pack_static (377u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_46_72_65_71_75_65_6E_63_79 : LocalName = LocalName :: pack_static (378u32) ;
pub const ATOM_LOCALNAME__6F_70_65_72_61_74_6F_72 : LocalName = LocalName :: pack_static (379u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (380u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (381u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_6D_69_64 : LocalName = LocalName :: pack_static (382u32) ;
pub const ATOM_LOCALNAME__6D_6C_61_62_65_6C_65_64_74_72 : LocalName = LocalName :: pack_static (383u32) ;
pub const ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_69_74_65_6D : LocalName = LocalName :: pack_static (384u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_48_65_69_67_68_74 : LocalName = LocalName :: pack_static (385u32) ;
pub const ATOM_LOCALNAME__66_69_6C_6C_2D_72_75_6C_65 : LocalName = LocalName :: pack_static (386u32) ;
pub const ATOM_LOCALNAME__69_64_65_6F_67_72_61_70_68_69_63 : LocalName = LocalName :: pack_static (387u32) ;
pub const ATOM_LOCALNAME__79_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (388u32) ;
pub const ATOM_LOCALNAME__64_61_74_65_74_69_6D_65 : LocalName = LocalName :: pack_static (389u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_74_68_69_63_6B_6E_65_73_73 : LocalName = LocalName :: pack_static (390u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_69_6E : LocalName = LocalName :: pack_static (391u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_57_69_64_74_68 : LocalName = LocalName :: pack_static (392u32) ;
pub const ATOM_LOCALNAME__73_74_72_65_74_63_68_79 : LocalName = LocalName :: pack_static (393u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_68_6F_72_69_7A_6F_6E_74_61_6C : LocalName = LocalName :: pack_static (394u32) ;
pub const ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74_69_61_6C_65 : LocalName = LocalName :: pack_static (395u32) ;
pub const ATOM_LOCALNAME__65_64_67_65_6D_6F_64_65 : LocalName = LocalName :: pack_static (396u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (397u32) ;
pub const ATOM_LOCALNAME__66_65_6D_65_72_67_65_6E_6F_64_65 : LocalName = LocalName :: pack_static (398u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_68_61_73_70_6F_70_75_70 : LocalName = LocalName :: pack_static (399u32) ;
pub const ATOM_LOCALNAME__76_61_72_69_61_6E_63_65 : LocalName = LocalName :: pack_static (400u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E_2D_66_69_6C_74_65_72_73 : LocalName = LocalName :: pack_static (401u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_61_72_65_61 : LocalName = LocalName :: pack_static (402u32) ;
pub const ATOM_LOCALNAME__72_61_64_69_6F_67_72_6F_75_70 : LocalName = LocalName :: pack_static (403u32) ;
pub const ATOM_LOCALNAME__61_6C_6C_6F_77_66_75_6C_6C_73_63_72_65_65_6E : LocalName = LocalName :: pack_static (404u32) ;
pub const ATOM_LOCALNAME__64_6F_6D_61_69_6E_6F_66_61_70_70_6C_69_63_61_74_69_6F_6E : LocalName = LocalName :: pack_static (405u32) ;
pub const ATOM_LOCALNAME__66_65_4F_66_66_73_65_74 : LocalName = LocalName :: pack_static (406u32) ;
pub const ATOM_LOCALNAME__65_6D_70_74_79_73_65_74 : LocalName = LocalName :: pack_static (407u32) ;
pub const ATOM_LOCALNAME__6F_63_63_75_72_72_65_6E_63_65 : LocalName = LocalName :: pack_static (408u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_65_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (409u32) ;
pub const ATOM_LOCALNAME__63_61_6C_63_4D_6F_64_65 : LocalName = LocalName :: pack_static (410u32) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E_54_72_61_6E_73_66_6F_72_6D : LocalName = LocalName :: pack_static (411u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_67_65_68_69_64_65 : LocalName = LocalName :: pack_static (412u32) ;
pub const ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_6E_65_6E_74_54_72_61_6E_73_66_65_72 : LocalName = LocalName :: pack_static (413u32) ;
pub const ATOM_LOCALNAME__66_65_44_72_6F_70_53_68_61_64_6F_77 : LocalName = LocalName :: pack_static (414u32) ;
pub const ATOM_LOCALNAME__69_6E_74_65_72_73_65_63_74 : LocalName = LocalName :: pack_static (415u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_78 : LocalName = LocalName :: pack_static (416u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_75_72_6C : LocalName = LocalName :: pack_static (417u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_72_6F_6C_65 : LocalName = LocalName :: pack_static (418u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74_75_6E_64_65_72 : LocalName = LocalName :: pack_static (419u32) ;
pub const ATOM_LOCALNAME__73_74_6F_70_2D_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (420u32) ;
pub const ATOM_LOCALNAME__66_65_4D_65_72_67_65_4E_6F_64_65 : LocalName = LocalName :: pack_static (421u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_69_74_6C_65 : LocalName = LocalName :: pack_static (422u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_77_65_69_67_68_74 : LocalName = LocalName :: pack_static (423u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_66_6C_6F_77_74_6F : LocalName = LocalName :: pack_static (424u32) ;
pub const ATOM_LOCALNAME__66_65_64_69_73_74_61_6E_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (425u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_70_64_61_74_65 : LocalName = LocalName :: pack_static (426u32) ;
pub const ATOM_LOCALNAME__76_2D_61_6C_70_68_61_62_65_74_69_63 : LocalName = LocalName :: pack_static (427u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (428u32) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6D_69_74_65_72_6C_69_6D_69_74 : LocalName = LocalName :: pack_static (429u32) ;
pub const ATOM_LOCALNAME__64_69_76_65_72_67_65_6E_63_65 : LocalName = LocalName :: pack_static (430u32) ;
pub const ATOM_LOCALNAME__6C_69_6E_65_61_72_47_72_61_64_69_65_6E_74 : LocalName = LocalName :: pack_static (431u32) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_43_6F_6E_65_41_6E_67_6C_65 : LocalName = LocalName :: pack_static (432u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_2D_70_61_74_68 : LocalName = LocalName :: pack_static (433u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_6C_65_76_65_6C : LocalName = LocalName :: pack_static (434u32) ;
pub const ATOM_LOCALNAME__6F_6E_75_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (435u32) ;
pub const ATOM_LOCALNAME__6D_75_6E_64_65_72_6F_76_65_72 : LocalName = LocalName :: pack_static (436u32) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74_43_6F_75_6E_74 : LocalName = LocalName :: pack_static (437u32) ;
pub const ATOM_LOCALNAME__70_6C_61_63_65_68_6F_6C_64_65_72 : LocalName = LocalName :: pack_static (438u32) ;
pub const ATOM_LOCALNAME__76_65_63_74_6F_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (439u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_6E : LocalName = LocalName :: pack_static (440u32) ;
pub const ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E : LocalName = LocalName :: pack_static (441u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65_2D_73_68_69_66_74 : LocalName = LocalName :: pack_static (442u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_73_65_63_72_65_74 : LocalName = LocalName :: pack_static (443u32) ;
pub const ATOM_LOCALNAME__72_65_66_65_72_72_65_72_70_6F_6C_69_63_79 : LocalName = LocalName :: pack_static (444u32) ;
pub const ATOM_LOCALNAME__63_61_6C_63_6D_6F_64_65 : LocalName = LocalName :: pack_static (445u32) ;
pub const ATOM_LOCALNAME__6F_6E_68_61_73_68_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (446u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_61_6C_69_67_6E : LocalName = LocalName :: pack_static (447u32) ;
pub const ATOM_LOCALNAME__6D_61_72_67_69_6E_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (448u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_67_72_61_62 : LocalName = LocalName :: pack_static (449u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_69_66_69_63_61_74_69_6F_6E : LocalName = LocalName :: pack_static (450u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_65_6E_63_74_79_70_65 : LocalName = LocalName :: pack_static (451u32) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_63_6F_6E_65_61_6E_67_6C_65 : LocalName = LocalName :: pack_static (452u32) ;
pub const ATOM_LOCALNAME__63_61_72_74_65_73_69_61_6E_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (453u32) ;
pub const ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E : LocalName = LocalName :: pack_static (454u32) ;
pub const ATOM_LOCALNAME__66_61_63_74_6F_72_6F_66 : LocalName = LocalName :: pack_static (455u32) ;
pub const ATOM_LOCALNAME__6D_6F_6D_65_6E_74_61_62_6F_75_74 : LocalName = LocalName :: pack_static (456u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_61_78 : LocalName = LocalName :: pack_static (457u32) ;
pub const ATOM_LOCALNAME__6C_65_6E_67_74_68_41_64_6A_75_73_74 : LocalName = LocalName :: pack_static (458u32) ;
pub const ATOM_LOCALNAME__6D_65_64_69_75_6D_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (459u32) ;
pub const ATOM_LOCALNAME__70_61_6E_6F_73_65_2D_31 : LocalName = LocalName :: pack_static (460u32) ;
pub const ATOM_LOCALNAME__6D_6F_76_61_62_6C_65_6C_69_6D_69_74_73 : LocalName = LocalName :: pack_static (461u32) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72_75_6E_69_74_73 : LocalName = LocalName :: pack_static (462u32) ;
pub const ATOM_LOCALNAME__73_74_69_74_63_68_54_69_6C_65_73 : LocalName = LocalName :: pack_static (463u32) ;
pub const ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_75_6E_69_74_73 : LocalName = LocalName :: pack_static (464u32) ;
pub const ATOM_LOCALNAME__66_61_63_74_6F_72_69_61_6C : LocalName = LocalName :: pack_static (465u32) ;
pub const ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_6E_65_6E_74_74_72_61_6E_73_66_65_72 : LocalName = LocalName :: pack_static (466u32) ;
pub const ATOM_LOCALNAME__73_79_73_74_65_6D_4C_61_6E_67_75_61_67_65 : LocalName = LocalName :: pack_static (467u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_6E_6C_6F_61_64 : LocalName = LocalName :: pack_static (468u32) ;
pub const ATOM_LOCALNAME__6E_6F_72_65_73_69_7A_65 : LocalName = LocalName :: pack_static (469u32) ;
pub const ATOM_LOCALNAME__64_6F_6D_69_6E_61_6E_74_2D_62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (470u32) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_69_6F_6E : LocalName = LocalName :: pack_static (471u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_73_70_65_63_74_52_61_74_69_6F : LocalName = LocalName :: pack_static (472u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_70_61_74_68_75_6E_69_74_73 : LocalName = LocalName :: pack_static (473u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_55_6E_69_74_73 : LocalName = LocalName :: pack_static (474u32) ;
pub const ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79_69 : LocalName = LocalName :: pack_static (475u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_6E_74_65_78_74_6D_65_6E_75 : LocalName = LocalName :: pack_static (476u32) ;
pub const ATOM_LOCALNAME__6E_6F_66_72_61_6D_65_73 : LocalName = LocalName :: pack_static (477u32) ;
pub const ATOM_LOCALNAME__72_6F_77_61_6C_69_67_6E : LocalName = LocalName :: pack_static (478u32) ;
pub const ATOM_LOCALNAME__68_6F_72_69_7A_2D_61_64_76_2D_78 : LocalName = LocalName :: pack_static (479u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_72_63_72_6F_6C_65 : LocalName = LocalName :: pack_static (480u32) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_5A : LocalName = LocalName :: pack_static (481u32) ;
pub const ATOM_LOCALNAME__73_75_72_66_61_63_65_53_63_61_6C_65 : LocalName = LocalName :: pack_static (482u32) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_76_65_72_74_69_63_61_6C : LocalName = LocalName :: pack_static (483u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_77_69_64_74_68 : LocalName = LocalName :: pack_static (484u32) ;
pub const ATOM_LOCALNAME__6D_61_6E_69_66_65_73_74 : LocalName = LocalName :: pack_static (485u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_79_70_65 : LocalName = LocalName :: pack_static (486u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_61_6E_63_68_6F_72 : LocalName = LocalName :: pack_static (487u32) ;
pub const ATOM_LOCALNAME__64_65_63_6F_64_69_6E_67 : LocalName = LocalName :: pack_static (488u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_6E_73_3A_78_6C_69_6E_6B : LocalName = LocalName :: pack_static (489u32) ;
pub const ATOM_LOCALNAME__6E_6F_73_63_72_69_70_74 : LocalName = LocalName :: pack_static (490u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74 : LocalName = LocalName :: pack_static (491u32) ;
pub const ATOM_LOCALNAME__78_6D_6C_3A_73_70_61_63_65 : LocalName = LocalName :: pack_static (492u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_69_6E_76_61_6C_69_64 : LocalName = LocalName :: pack_static (493u32) ;
pub const ATOM_LOCALNAME__74_72_61_6E_73_70_6F_73_65 : LocalName = LocalName :: pack_static (494u32) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (495u32) ;
pub const ATOM_LOCALNAME__70_72_6F_70_65_72_74_79 : LocalName = LocalName :: pack_static (496u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_70_72_65_73_73_65_64 : LocalName = LocalName :: pack_static (497u32) ;
pub const ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_2D_73_72_63 : LocalName = LocalName :: pack_static (498u32) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65_56_61_6C_75_65_73 : LocalName = LocalName :: pack_static (499u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_61_63_74_69_76_65_64_65_73_63_65_6E_64_61_6E_74 : LocalName = LocalName :: pack_static (500u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_2D_72_65_6E_64_65_72_69_6E_67 : LocalName = LocalName :: pack_static (501u32) ;
pub const ATOM_LOCALNAME__73_65_6D_61_6E_74_69_63_73 : LocalName = LocalName :: pack_static (502u32) ;
pub const ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72_73 : LocalName = LocalName :: pack_static (503u32) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74_6D_69_6E_73_69_7A_65 : LocalName = LocalName :: pack_static (504u32) ;
pub const ATOM_LOCALNAME__66_69_6C_6C_2D_6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_static (505u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_76_61_72_69_61_6E_74 : LocalName = LocalName :: pack_static (506u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6C_61_62_65_6C_6C_65_64_62_79 : LocalName = LocalName :: pack_static (507u32) ;
pub const ATOM_LOCALNAME__63_6F_6C_75_6D_6E_77_69_64_74_68 : LocalName = LocalName :: pack_static (508u32) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_2D_62_61_73_65_6C_69_6E_65 : LocalName = LocalName :: pack_static (509u32) ;
pub const ATOM_LOCALNAME__66_65_70_6F_69_6E_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (510u32) ;
pub const ATOM_LOCALNAME__78_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 : LocalName = LocalName :: pack_static (511u32) ;
pub const ATOM_LOCALNAME__66_6F_72_65_69_67_6E_4F_62_6A_65_63_74 : LocalName = LocalName :: pack_static (512u32) ;
pub const ATOM_LOCALNAME__62_65_76_65_6C_6C_65_64 : LocalName = LocalName :: pack_static (513u32) ;
pub const ATOM_LOCALNAME__66_65_54_75_72_62_75_6C_65_6E_63_65 : LocalName = LocalName :: pack_static (514u32) ;
pub const ATOM_LOCALNAME__66_65_73_70_6F_74_6C_69_67_68_74 : LocalName = LocalName :: pack_static (515u32) ;
pub const ATOM_LOCALNAME__70_72_73_75_62_73_65_74 : LocalName = LocalName :: pack_static (516u32) ;
pub const ATOM_LOCALNAME__62_61_73_65_66_72_65_71_75_65_6E_63_79 : LocalName = LocalName :: pack_static (517u32) ;
pub const ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_75_6E_69_74_73 : LocalName = LocalName :: pack_static (518u32) ;
pub const ATOM_LOCALNAME__61_75_74_6F_73_75_62_6D_69_74 : LocalName = LocalName :: pack_static (519u32) ;
pub const ATOM_LOCALNAME__65_75_6C_65_72_67_61_6D_6D_61 : LocalName = LocalName :: pack_static (520u32) ;
pub const ATOM_LOCALNAME__6F_6E_69_6E_76_61_6C_69_64 : LocalName = LocalName :: pack_static (521u32) ;
pub const ATOM_LOCALNAME__64_72_61_67_67_61_62_6C_65 : LocalName = LocalName :: pack_static (522u32) ;
pub const ATOM_LOCALNAME__70_6F_6C_79_6C_69_6E_65 : LocalName = LocalName :: pack_static (523u32) ;
pub const ATOM_LOCALNAME__6D_61_6C_69_67_6E_67_72_6F_75_70 : LocalName = LocalName :: pack_static (524u32) ;
pub const ATOM_LOCALNAME__74_65_78_74_4C_65_6E_67_74_68 : LocalName = LocalName :: pack_static (525u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_75_62_6D_69_74 : LocalName = LocalName :: pack_static (526u32) ;
pub const ATOM_LOCALNAME__6D_6D_75_6C_74_69_73_63_72_69_70_74_73 : LocalName = LocalName :: pack_static (527u32) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_63_72_69_70_74_54_79_70_65 : LocalName = LocalName :: pack_static (528u32) ;
pub const ATOM_LOCALNAME__63_6F_64_6F_6D_61_69_6E : LocalName = LocalName :: pack_static (529u32) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_6E_74_72_6F_6C_73_65_6C_65_63_74 : LocalName = LocalName :: pack_static (530u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_62_6C_63_6C_69_63_6B : LocalName = LocalName :: pack_static (531u32) ;
pub const ATOM_LOCALNAME__61_63_63_65_73_73_6B_65_79 : LocalName = LocalName :: pack_static (532u32) ;
pub const ATOM_LOCALNAME__6D_65_6E_75_69_74_65_6D : LocalName = LocalName :: pack_static (533u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_63_68_61_6E_6E_65_6C : LocalName = LocalName :: pack_static (534u32) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_74_61_72_67_65_74 : LocalName = LocalName :: pack_static (535u32) ;
pub const ATOM_LOCALNAME__6F_6E_66_69_6E_69_73_68 : LocalName = LocalName :: pack_static (536u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_70_61_74_68 : LocalName = LocalName :: pack_static (537u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_6C_65_61_76_65 : LocalName = LocalName :: pack_static (538u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_64_72_6F_70 : LocalName = LocalName :: pack_static (539u32) ;
pub const ATOM_LOCALNAME__61_63_74_69_6F_6E_74_79_70_65 : LocalName = LocalName :: pack_static (540u32) ;
pub const ATOM_LOCALNAME__73_6F_6C_69_64_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (541u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_79_6C_65 : LocalName = LocalName :: pack_static (542u32) ;
pub const ATOM_LOCALNAME__6F_75_74_65_72_70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_static (543u32) ;
pub const ATOM_LOCALNAME__6E_6F_74_61_6E_75_6D_62_65_72 : LocalName = LocalName :: pack_static (544u32) ;
pub const ATOM_LOCALNAME__72_65_71_75_69_72_65_64_46_65_61_74_75_72_65_73 : LocalName = LocalName :: pack_static (545u32) ;
pub const ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74_73_74_61_72_74 : LocalName = LocalName :: pack_static (546u32) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67_73_74_61_72_74 : LocalName = LocalName :: pack_static (547u32) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_73_63_6F_70_65 : LocalName = LocalName :: pack_static (548u32) ;
pub const ATOM_LOCALNAME__73_63_72_6F_6C_6C_69_6E_67 : LocalName = LocalName :: pack_static (549u32) ;
pub const ATOM_LOCALNAME__6F_6E_70_72_6F_70_65_72_74_79_63_68_61_6E_67_65 : LocalName = LocalName :: pack_static (550u32) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_70_72_65_73_73 : LocalName = LocalName :: pack_static (551u32) ;
pub const ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E_2D_78_6D_6C : LocalName = LocalName :: pack_static (552u32) ;
pub const ATOM_LOCALNAME__73_74_64_44_65_76_69_61_74_69_6F_6E : LocalName = LocalName :: pack_static (553u32) ;
pub const ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_45_78_70_6F_6E_65_6E_74 : LocalName = LocalName :: pack_static (554u32) ;
pub const ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (555u32) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65_73_65_74 : LocalName = LocalName :: pack_static (556u32) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_64_6F_77_6E : LocalName = LocalName :: pack_static (557u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_73_65_6C_65_63_74_61_62_6C_65 : LocalName = LocalName :: pack_static (558u32) ;
pub const ATOM_LOCALNAME__66_6F_6E_74_73_69_7A_65 : LocalName = LocalName :: pack_static (559u32) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_77_68_65_65_6C : LocalName = LocalName :: pack_static (560u32) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_75_74 : LocalName = LocalName :: pack_static (561u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_6C_70_68_61 : LocalName = LocalName :: pack_static (562u32) ;
pub const ATOM_LOCALNAME__6D_61_74_68_63_6F_6C_6F_72 : LocalName = LocalName :: pack_static (563u32) ;
pub const ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_4E_61_6D_65 : LocalName = LocalName :: pack_static (564u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_64_69_73_61_62_6C_65_64 : LocalName = LocalName :: pack_static (565u32) ;
pub const ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_73_70_65_63_74_72_61_74_69_6F : LocalName = LocalName :: pack_static (566u32) ;
pub const ATOM_LOCALNAME__76_2D_69_64_65_6F_67_72_61_70_68_69_63 : LocalName = LocalName :: pack_static (567u32) ;
pub const ATOM_LOCALNAME__74_68_69_6E_6D_61_74_68_73_70_61_63_65 : LocalName = LocalName :: pack_static (568u32) ;
pub const ATOM_LOCALNAME__6F_6E_6C_6F_73_65_63_61_70_74_75_72_65 : LocalName = LocalName :: pack_static (569u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_70_6F_73_69_6E_73_65_74 : LocalName = LocalName :: pack_static (570u32) ;
pub const ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72 : LocalName = LocalName :: pack_static (571u32) ;
pub const ATOM_LOCALNAME__6D_75_6C_74_69_63_6F_6C : LocalName = LocalName :: pack_static (572u32) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_63_74_75_61_74_65 : LocalName = LocalName :: pack_static (573u32) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72_68_65_69_67_68_74 : LocalName = LocalName :: pack_static (574u32) ;
pub const ATOM_LOCALNAME__63_6C_69_70_50_61_74_68 : LocalName = LocalName :: pack_static (575u32) ;
pub const ATOM_LOCALNAME__62_6C_6F_63_6B_71_75_6F_74_65 : LocalName = LocalName :: pack_static (576u32) ;
pub const ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6E_6F_77 : LocalName = LocalName :: pack_static (577u32) ;
pub const ATOM_LOCALNAME__2A : LocalName = LocalName :: pack_inline (10752u64 , 1u8) ;
pub const ATOM_LOCALNAME__61 : LocalName = LocalName :: pack_inline (24832u64 , 1u8) ;
pub const ATOM_LOCALNAME__61_62_62_72 : LocalName = LocalName :: pack_inline (491276886272u64 , 4u8) ;
pub const ATOM_LOCALNAME__61_62_73 : LocalName = LocalName :: pack_inline (1935827200u64 , 3u8) ;
pub const ATOM_LOCALNAME__61_63_63_65_6E_74 : LocalName = LocalName :: pack_inline (32772479036645632u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_63_63_65_70_74 : LocalName = LocalName :: pack_inline (32774678059901184u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_63_72_6F_6E_79_6D : LocalName = LocalName :: pack_inline (7888457647255675136u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_63_74_69_6F_6E : LocalName = LocalName :: pack_inline (31084746153091328u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_63_74_69_76_65 : LocalName = LocalName :: pack_inline (28559167944089856u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_63_74_75_61_74_65 : LocalName = LocalName :: pack_inline (7310575252220895488u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_64_64_72_65_73_73 : LocalName = LocalName :: pack_inline (8319104478668415232u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_6C_69_67_6E : LocalName = LocalName :: pack_inline (121390429397248u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_6C_69_6E_6B : LocalName = LocalName :: pack_inline (118121959284992u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_6C_74 : LocalName = LocalName :: pack_inline (1953259776u64 , 3u8) ;
pub const ATOM_LOCALNAME__61_6C_74_69_6D_67 : LocalName = LocalName :: pack_inline (29112222293451008u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_6C_74_74_65_78_74 : LocalName = LocalName :: pack_inline (8392569456448790784u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_6E_64 : LocalName = LocalName :: pack_inline (1684955392u64 , 3u8) ;
pub const ATOM_LOCALNAME__61_6E_69_6D_61_74_65 : LocalName = LocalName :: pack_inline (7310575217677328640u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_70_70_6C_65_74 : LocalName = LocalName :: pack_inline (32762613715722496u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_70_70_6C_79 : LocalName = LocalName :: pack_inline (133506649841920u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_70_70_72_6F_78 : LocalName = LocalName :: pack_inline (33899534508646656u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_73 : LocalName = LocalName :: pack_inline (32492094982611200u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_73_68 : LocalName = LocalName :: pack_inline (7526481874927116544u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_74 : LocalName = LocalName :: pack_inline (32773569959321856u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_6F_74_68 : LocalName = LocalName :: pack_inline (7526763349903827200u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_73_63 : LocalName = LocalName :: pack_inline (27992893401751808u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_63_73_63_68 : LocalName = LocalName :: pack_inline (7521982673346257152u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_68_69_76_65 : LocalName = LocalName :: pack_inline (7311146942148534528u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_72_6F_6C_65 : LocalName = LocalName :: pack_inline (7308338832400867584u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_73_65_63 : LocalName = LocalName :: pack_inline (27977568958439680u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_73_65_63_68 : LocalName = LocalName :: pack_inline (7521967348902945024u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_73_69_6E : LocalName = LocalName :: pack_inline (31078191748768000u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_73_69_6E_68 : LocalName = LocalName :: pack_inline (7525067971693273344u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_63_74_61_6E : LocalName = LocalName :: pack_inline (31069399950713088u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_72_63_74_61_6E_68 : LocalName = LocalName :: pack_inline (7525059179895218432u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_72_65_61 : LocalName = LocalName :: pack_inline (418313822464u64 , 4u8) ;
pub const ATOM_LOCALNAME__61_72_67 : LocalName = LocalName :: pack_inline (1735549184u64 , 3u8) ;
pub const ATOM_LOCALNAME__61_72_74_69_63_6C_65 : LocalName = LocalName :: pack_inline (7308325599891841280u64 , 7u8) ;
pub const ATOM_LOCALNAME__61_73_63_65_6E_74 : LocalName = LocalName :: pack_inline (32772479037694208u64 , 6u8) ;
pub const ATOM_LOCALNAME__61_73_69_64_65 : LocalName = LocalName :: pack_inline (111481940304128u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_73_79_6E_63 : LocalName = LocalName :: pack_inline (109326135156992u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_75_64_69_6F : LocalName = LocalName :: pack_inline (122498447663360u64 , 5u8) ;
pub const ATOM_LOCALNAME__61_78_69_73 : LocalName = LocalName :: pack_inline (495690735872u64 , 4u8) ;
pub const ATOM_LOCALNAME__61_7A_69_6D_75_74_68 : LocalName = LocalName :: pack_inline (7526769990024454400u64 , 7u8) ;
pub const ATOM_LOCALNAME__62 : LocalName = LocalName :: pack_inline (25088u64 , 1u8) ;
pub const ATOM_LOCALNAME__62_61_73_65 : LocalName = LocalName :: pack_inline (435727458816u64 , 4u8) ;
pub const ATOM_LOCALNAME__62_62_6F_78 : LocalName = LocalName :: pack_inline (517264794112u64 , 4u8) ;
pub const ATOM_LOCALNAME__62_64_69 : LocalName = LocalName :: pack_inline (1768186368u64 , 3u8) ;
pub const ATOM_LOCALNAME__62_64_6F : LocalName = LocalName :: pack_inline (1868849664u64 , 3u8) ;
pub const ATOM_LOCALNAME__62_65_67_69_6E : LocalName = LocalName :: pack_inline (121398985318912u64 , 5u8) ;
pub const ATOM_LOCALNAME__62_67_63_6F_6C_6F_72 : LocalName = LocalName :: pack_inline (8245928668403556864u64 , 7u8) ;
pub const ATOM_LOCALNAME__62_67_73_6F_75_6E_64 : LocalName = LocalName :: pack_inline (7236850772768940544u64 , 7u8) ;
pub const ATOM_LOCALNAME__62_69_61_73 : LocalName = LocalName :: pack_inline (495555535360u64 , 4u8) ;
pub const ATOM_LOCALNAME__62_69_67 : LocalName = LocalName :: pack_inline (1734959616u64 , 3u8) ;
pub const ATOM_LOCALNAME__62_6C_69_6E_6B : LocalName = LocalName :: pack_inline (118121959285248u64 , 5u8) ;
pub const ATOM_LOCALNAME__62_6F_64_79 : LocalName = LocalName :: pack_inline (521376064000u64 , 4u8) ;
pub const ATOM_LOCALNAME__62_6F_72_64_65_72 : LocalName = LocalName :: pack_inline (32199629436051968u64 , 6u8) ;
pub const ATOM_LOCALNAME__62_72 : LocalName = LocalName :: pack_inline (7496192u64 , 2u8) ;
pub const ATOM_LOCALNAME__62_75_74_74_6F_6E : LocalName = LocalName :: pack_inline (31084793398911488u64 , 6u8) ;
pub const ATOM_LOCALNAME__62_76_61_72 : LocalName = LocalName :: pack_inline (491261420032u64 , 4u8) ;
pub const ATOM_LOCALNAME__62_79 : LocalName = LocalName :: pack_inline (7954944u64 , 2u8) ;
pub const ATOM_LOCALNAME__63_61_6E_76_61_73 : LocalName = LocalName :: pack_inline (32476783607636736u64 , 6u8) ;
pub const ATOM_LOCALNAME__63_61_70_74_69_6F_6E : LocalName = LocalName :: pack_inline (7957695015409509120u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_61_72_64 : LocalName = LocalName :: pack_inline (431415714560u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_65_69_6C_69_6E_67 : LocalName = LocalName :: pack_inline (7453010347690386176u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_65_6E_74_65_72 : LocalName = LocalName :: pack_inline (32199698087764736u64 , 6u8) ;
pub const ATOM_LOCALNAME__63_68_61_72 : LocalName = LocalName :: pack_inline (491260502784u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_68_61_72_6F_66_66 : LocalName = LocalName :: pack_inline (7378707576544322304u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_68_61_72_73_65_74 : LocalName = LocalName :: pack_inline (8387236816145113856u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_68_65_63_6B_65_64 : LocalName = LocalName :: pack_inline (7234306451087844096u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_69 : LocalName = LocalName :: pack_inline (6906624u64 , 2u8) ;
pub const ATOM_LOCALNAME__63_69_72_63_6C_65 : LocalName = LocalName :: pack_inline (28548147024847616u64 , 6u8) ;
pub const ATOM_LOCALNAME__63_69_74_65 : LocalName = LocalName :: pack_inline (435744760576u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_6C_61_73_73 : LocalName = LocalName :: pack_inline (126939392926464u64 , 5u8) ;
pub const ATOM_LOCALNAME__63_6C_61_73_73_69_64 : LocalName = LocalName :: pack_inline (7235441215740338944u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6C_65_61_72 : LocalName = LocalName :: pack_inline (125762638996224u64 , 5u8) ;
pub const ATOM_LOCALNAME__63_6C_69_70 : LocalName = LocalName :: pack_inline (482805048064u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_6C_6F_73_65 : LocalName = LocalName :: pack_inline (111546465018624u64 , 5u8) ;
pub const ATOM_LOCALNAME__63_6C_6F_73_75_72_65 : LocalName = LocalName :: pack_inline (7310034283826799360u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6E : LocalName = LocalName :: pack_inline (7234304u64 , 2u8) ;
pub const ATOM_LOCALNAME__63_6F_64_65 : LocalName = LocalName :: pack_inline (435476718336u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_6F_6C : LocalName = LocalName :: pack_inline (1819239168u64 , 3u8) ;
pub const ATOM_LOCALNAME__63_6F_6C_6F_72 : LocalName = LocalName :: pack_inline (125822886175488u64 , 5u8) ;
pub const ATOM_LOCALNAME__63_6F_6C_73 : LocalName = LocalName :: pack_inline (495740478208u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_6F_6C_73_70_61_6E : LocalName = LocalName :: pack_inline (7953762057955795712u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_61_63_74 : LocalName = LocalName :: pack_inline (8386654066594243328u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6F_6D_70_6F_73_65 : LocalName = LocalName :: pack_inline (7310309148815483648u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6F_6E_74_65_6E_74 : LocalName = LocalName :: pack_inline (8389754706581218048u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_6F_6F_72_64_73 : LocalName = LocalName :: pack_inline (32480064980345600u64 , 6u8) ;
pub const ATOM_LOCALNAME__63_6F_73 : LocalName = LocalName :: pack_inline (1936679680u64 , 3u8) ;
pub const ATOM_LOCALNAME__63_6F_73_68 : LocalName = LocalName :: pack_inline (448613278464u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_6F_74 : LocalName = LocalName :: pack_inline (1953456896u64 , 3u8) ;
pub const ATOM_LOCALNAME__63_6F_74_68 : LocalName = LocalName :: pack_inline (448630055680u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_73_63 : LocalName = LocalName :: pack_inline (1668506368u64 , 3u8) ;
pub const ATOM_LOCALNAME__63_73_63_68 : LocalName = LocalName :: pack_inline (448345105152u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_73_79_6D_62_6F_6C : LocalName = LocalName :: pack_inline (7813572100839662336u64 , 7u8) ;
pub const ATOM_LOCALNAME__63_75_72_6C : LocalName = LocalName :: pack_inline (465776763648u64 , 4u8) ;
pub const ATOM_LOCALNAME__63_75_72_73_6F_72 : LocalName = LocalName :: pack_inline (32210688977232640u64 , 6u8) ;
pub const ATOM_LOCALNAME__63_78 : LocalName = LocalName :: pack_inline (7889664u64 , 2u8) ;
pub const ATOM_LOCALNAME__63_79 : LocalName = LocalName :: pack_inline (7955200u64 , 2u8) ;
pub const ATOM_LOCALNAME__64 : LocalName = LocalName :: pack_inline (25600u64 , 1u8) ;
pub const ATOM_LOCALNAME__64_61_74_61 : LocalName = LocalName :: pack_inline (418564367360u64 , 4u8) ;
pub const ATOM_LOCALNAME__64_61_74_61_66_6C_64 : LocalName = LocalName :: pack_inline (7236271270027944960u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_61_74_61_73_72_63 : LocalName = LocalName :: pack_inline (7165916819501442048u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_64 : LocalName = LocalName :: pack_inline (6579200u64 , 2u8) ;
pub const ATOM_LOCALNAME__64_65_63_6C_61_72_65 : LocalName = LocalName :: pack_inline (7310012263327687680u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_65_66_61_75_6C_74 : LocalName = LocalName :: pack_inline (8389209267074589696u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_65_66_65_72 : LocalName = LocalName :: pack_inline (125779835184128u64 , 5u8) ;
pub const ATOM_LOCALNAME__64_65_66_73 : LocalName = LocalName :: pack_inline (495639159808u64 , 4u8) ;
pub const ATOM_LOCALNAME__64_65_67_72_65_65 : LocalName = LocalName :: pack_inline (28540514683151360u64 , 6u8) ;
pub const ATOM_LOCALNAME__64_65_6C : LocalName = LocalName :: pack_inline (1818584064u64 , 3u8) ;
pub const ATOM_LOCALNAME__64_65_70_74_68 : LocalName = LocalName :: pack_inline (114849311187968u64 , 5u8) ;
pub const ATOM_LOCALNAME__64_65_73_63 : LocalName = LocalName :: pack_inline (427137786880u64 , 4u8) ;
pub const ATOM_LOCALNAME__64_65_73_63_65_6E_74 : LocalName = LocalName :: pack_inline (8389754633650004992u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_65_74_61_69_6C_73 : LocalName = LocalName :: pack_inline (8317138479132009472u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_66_6E : LocalName = LocalName :: pack_inline (1852204032u64 , 3u8) ;
pub const ATOM_LOCALNAME__64_69_61_6C_6F_67 : LocalName = LocalName :: pack_inline (29114433882645504u64 , 6u8) ;
pub const ATOM_LOCALNAME__64_69_66_66 : LocalName = LocalName :: pack_inline (439804847104u64 , 4u8) ;
pub const ATOM_LOCALNAME__64_69_72 : LocalName = LocalName :: pack_inline (1919509504u64 , 3u8) ;
pub const ATOM_LOCALNAME__64_69_72_6E_61_6D_65 : LocalName = LocalName :: pack_inline (7308604897285989376u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_69_73_63_61_72_64 : LocalName = LocalName :: pack_inline (7237954630903751680u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_69_73_70_6C_61_79 : LocalName = LocalName :: pack_inline (8746391181558637568u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_69_76 : LocalName = LocalName :: pack_inline (1986618368u64 , 3u8) ;
pub const ATOM_LOCALNAME__64_69_76_69_64_65 : LocalName = LocalName :: pack_inline (28539376768738304u64 , 6u8) ;
pub const ATOM_LOCALNAME__64_69_76_69_73_6F_72 : LocalName = LocalName :: pack_inline (8245936339534046208u64 , 7u8) ;
pub const ATOM_LOCALNAME__64_6C : LocalName = LocalName :: pack_inline (7103488u64 , 2u8) ;
pub const ATOM_LOCALNAME__64_6F_6D_61_69_6E : LocalName = LocalName :: pack_inline (31078114606932992u64 , 6u8) ;
pub const ATOM_LOCALNAME__64_74 : LocalName = LocalName :: pack_inline (7627776u64 , 2u8) ;
pub const ATOM_LOCALNAME__64_75_72 : LocalName = LocalName :: pack_inline (1920295936u64 , 3u8) ;
pub const ATOM_LOCALNAME__64_78 : LocalName = LocalName :: pack_inline (7889920u64 , 2u8) ;
pub const ATOM_LOCALNAME__64_79 : LocalName = LocalName :: pack_inline (7955456u64 , 2u8) ;
pub const ATOM_LOCALNAME__65_64_67_65 : LocalName = LocalName :: pack_inline (435526329600u64 , 4u8) ;
pub const ATOM_LOCALNAME__65_6C_6C_69_70_73_65 : LocalName = LocalName :: pack_inline (7310310218245367040u64 , 7u8) ;
pub const ATOM_LOCALNAME__65_6D : LocalName = LocalName :: pack_inline (7169280u64 , 2u8) ;
pub const ATOM_LOCALNAME__65_6D_62_65_64 : LocalName = LocalName :: pack_inline (110386605810944u64 , 5u8) ;
pub const ATOM_LOCALNAME__65_6E_63_74_79_70_65 : LocalName = LocalName :: pack_inline (7309475736013661440u64 , 7u8) ;
pub const ATOM_LOCALNAME__65_6E_64 : LocalName = LocalName :: pack_inline (1684956416u64 , 3u8) ;
pub const ATOM_LOCALNAME__65_71 : LocalName = LocalName :: pack_inline (7431424u64 , 2u8) ;
pub const ATOM_LOCALNAME__65_76_65_6E_74 : LocalName = LocalName :: pack_inline (128017497482496u64 , 5u8) ;
pub const ATOM_LOCALNAME__65_78_69_73_74_73 : LocalName = LocalName :: pack_inline (32497661361284352u64 , 6u8) ;
pub const ATOM_LOCALNAME__65_78_70 : LocalName = LocalName :: pack_inline (1886938368u64 , 3u8) ;
pub const ATOM_LOCALNAME__66_61_63_65 : LocalName = LocalName :: pack_inline (435459024384u64 , 4u8) ;
pub const ATOM_LOCALNAME__66_61_6C_73_65 : LocalName = LocalName :: pack_inline (111546413966848u64 , 5u8) ;
pub const ATOM_LOCALNAME__66_65_42_6C_65_6E_64 : LocalName = LocalName :: pack_inline (7236833166875780608u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_46_6C_6F_6F_64 : LocalName = LocalName :: pack_inline (7237125637035877888u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_41 : LocalName = LocalName :: pack_inline (4711731085130950144u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_42 : LocalName = LocalName :: pack_inline (4783788679168878080u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_47 : LocalName = LocalName :: pack_inline (5144076649358517760u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_46_75_6E_63_52 : LocalName = LocalName :: pack_inline (5936710183775725056u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_49_6D_61_67_65 : LocalName = LocalName :: pack_inline (7306916042442630656u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_4D_65_72_67_65 : LocalName = LocalName :: pack_inline (7306934699847673344u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_54_69_6C_65 : LocalName = LocalName :: pack_inline (28548172291073536u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_65_62_6C_65_6E_64 : LocalName = LocalName :: pack_inline (7236833167412651520u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_66_6C_6F_6F_64 : LocalName = LocalName :: pack_inline (7237125637572748800u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_61 : LocalName = LocalName :: pack_inline (7017574094881515008u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_62 : LocalName = LocalName :: pack_inline (7089631688919442944u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_67 : LocalName = LocalName :: pack_inline (7449919659109082624u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_66_75_6E_63_72 : LocalName = LocalName :: pack_inline (8242553193526289920u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_69_6D_61_67_65 : LocalName = LocalName :: pack_inline (7306916042979501568u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_6D_65_72_67_65 : LocalName = LocalName :: pack_inline (7306934700384544256u64 , 7u8) ;
pub const ATOM_LOCALNAME__66_65_6E_63_65 : LocalName = LocalName :: pack_inline (111477728306688u64 , 5u8) ;
pub const ATOM_LOCALNAME__66_65_74_63_68 : LocalName = LocalName :: pack_inline (114776363853312u64 , 5u8) ;
pub const ATOM_LOCALNAME__66_65_74_69_6C_65 : LocalName = LocalName :: pack_inline (28548172827944448u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_69_67_75_72_65 : LocalName = LocalName :: pack_inline (28554821219476992u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_69_6C_6C : LocalName = LocalName :: pack_inline (465675314688u64 , 4u8) ;
pub const ATOM_LOCALNAME__66_69_6C_74_65_72 : LocalName = LocalName :: pack_inline (32199698054473216u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_6C_6F_6F_72 : LocalName = LocalName :: pack_inline (125822936311296u64 , 5u8) ;
pub const ATOM_LOCALNAME__66_6E : LocalName = LocalName :: pack_inline (7235072u64 , 2u8) ;
pub const ATOM_LOCALNAME__66_6F_6E_74 : LocalName = LocalName :: pack_inline (500069000704u64 , 4u8) ;
pub const ATOM_LOCALNAME__66_6F_6F_74_65_72 : LocalName = LocalName :: pack_inline (32199698105198080u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_6F_72 : LocalName = LocalName :: pack_inline (1919903232u64 , 3u8) ;
pub const ATOM_LOCALNAME__66_6F_72_61_6C_6C : LocalName = LocalName :: pack_inline (30518463272281600u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_6F_72_6D : LocalName = LocalName :: pack_inline (470071338496u64 , 4u8) ;
pub const ATOM_LOCALNAME__66_6F_72_6D_61_74 : LocalName = LocalName :: pack_inline (32758219997668864u64 , 6u8) ;
pub const ATOM_LOCALNAME__66_72_61_6D_65 : LocalName = LocalName :: pack_inline (111520460727808u64 , 5u8) ;
pub const ATOM_LOCALNAME__66_72_6F_6D : LocalName = LocalName :: pack_inline (470021203456u64 , 4u8) ;
pub const ATOM_LOCALNAME__66_78 : LocalName = LocalName :: pack_inline (7890432u64 , 2u8) ;
pub const ATOM_LOCALNAME__66_79 : LocalName = LocalName :: pack_inline (7955968u64 , 2u8) ;
pub const ATOM_LOCALNAME__67 : LocalName = LocalName :: pack_inline (26368u64 , 1u8) ;
pub const ATOM_LOCALNAME__67_31 : LocalName = LocalName :: pack_inline (3237632u64 , 2u8) ;
pub const ATOM_LOCALNAME__67_32 : LocalName = LocalName :: pack_inline (3303168u64 , 2u8) ;
pub const ATOM_LOCALNAME__67_63_64 : LocalName = LocalName :: pack_inline (1684236032u64 , 3u8) ;
pub const ATOM_LOCALNAME__67_65_71 : LocalName = LocalName :: pack_inline (1902470912u64 , 3u8) ;
pub const ATOM_LOCALNAME__67_6C_79_70_68 : LocalName = LocalName :: pack_inline (114832282773248u64 , 5u8) ;
pub const ATOM_LOCALNAME__67_72_61_64 : LocalName = LocalName :: pack_inline (431131617024u64 , 4u8) ;
pub const ATOM_LOCALNAME__67_74 : LocalName = LocalName :: pack_inline (7628544u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_31 : LocalName = LocalName :: pack_inline (3237888u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_32 : LocalName = LocalName :: pack_inline (3303424u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_33 : LocalName = LocalName :: pack_inline (3368960u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_34 : LocalName = LocalName :: pack_inline (3434496u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_35 : LocalName = LocalName :: pack_inline (3500032u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_36 : LocalName = LocalName :: pack_inline (3565568u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_61_6E_64_6C_65_72 : LocalName = LocalName :: pack_inline (8243113871575967744u64 , 7u8) ;
pub const ATOM_LOCALNAME__68_61_6E_67_69_6E_67 : LocalName = LocalName :: pack_inline (7453010326299174912u64 , 7u8) ;
pub const ATOM_LOCALNAME__68_65_61_64 : LocalName = LocalName :: pack_inline (431130765312u64 , 4u8) ;
pub const ATOM_LOCALNAME__68_65_61_64_65_72 : LocalName = LocalName :: pack_inline (32199629150185472u64 , 6u8) ;
pub const ATOM_LOCALNAME__68_65_61_64_65_72_73 : LocalName = LocalName :: pack_inline (8318822943511898112u64 , 7u8) ;
pub const ATOM_LOCALNAME__68_65_69_67_68_74 : LocalName = LocalName :: pack_inline (32765890657609728u64 , 6u8) ;
pub const ATOM_LOCALNAME__68_67_72_6F_75_70 : LocalName = LocalName :: pack_inline (31654318912792576u64 , 6u8) ;
pub const ATOM_LOCALNAME__68_69_64_64_65_6E : LocalName = LocalName :: pack_inline (31073729293936640u64 , 6u8) ;
pub const ATOM_LOCALNAME__68_69_67_68 : LocalName = LocalName :: pack_inline (448411559936u64 , 4u8) ;
pub const ATOM_LOCALNAME__68_6B_65_72_6E : LocalName = LocalName :: pack_inline (121437606864896u64 , 5u8) ;
pub const ATOM_LOCALNAME__68_72 : LocalName = LocalName :: pack_inline (7497728u64 , 2u8) ;
pub const ATOM_LOCALNAME__68_72_65_66 : LocalName = LocalName :: pack_inline (439788660736u64 , 4u8) ;
pub const ATOM_LOCALNAME__68_73_70_61_63_65 : LocalName = LocalName :: pack_inline (28538242797365248u64 , 6u8) ;
pub const ATOM_LOCALNAME__68_74_6D_6C : LocalName = LocalName :: pack_inline (465692813312u64 , 4u8) ;
pub const ATOM_LOCALNAME__69 : LocalName = LocalName :: pack_inline (26880u64 , 1u8) ;
pub const ATOM_LOCALNAME__69_63_6F_6E : LocalName = LocalName :: pack_inline (474315188480u64 , 4u8) ;
pub const ATOM_LOCALNAME__69_64 : LocalName = LocalName :: pack_inline (6580480u64 , 2u8) ;
pub const ATOM_LOCALNAME__69_64_65_6E_74 : LocalName = LocalName :: pack_inline (128017496303872u64 , 5u8) ;
pub const ATOM_LOCALNAME__69_66_72_61_6D_65 : LocalName = LocalName :: pack_inline (28549237946345728u64 , 6u8) ;
pub const ATOM_LOCALNAME__69_6D_61_67_65 : LocalName = LocalName :: pack_inline (111494690597120u64 , 5u8) ;
pub const ATOM_LOCALNAME__69_6D_67 : LocalName = LocalName :: pack_inline (1735223552u64 , 3u8) ;
pub const ATOM_LOCALNAME__69_6D_70_6C_69_65_73 : LocalName = LocalName :: pack_inline (8315168201473091840u64 , 7u8) ;
pub const ATOM_LOCALNAME__69_6E : LocalName = LocalName :: pack_inline (7235840u64 , 2u8) ;
pub const ATOM_LOCALNAME__69_6E_32 : LocalName = LocalName :: pack_inline (846096640u64 , 3u8) ;
pub const ATOM_LOCALNAME__69_6E_64_65_78 : LocalName = LocalName :: pack_inline (132376871987456u64 , 5u8) ;
pub const ATOM_LOCALNAME__69_6E_70_75_74 : LocalName = LocalName :: pack_inline (128047746279680u64 , 5u8) ;
pub const ATOM_LOCALNAME__69_6E_73 : LocalName = LocalName :: pack_inline (1936615680u64 , 3u8) ;
pub const ATOM_LOCALNAME__69_6E_74 : LocalName = LocalName :: pack_inline (1953392896u64 , 3u8) ;
pub const ATOM_LOCALNAME__69_6E_76_65_72_73_65 : LocalName = LocalName :: pack_inline (7310312400256657664u64 , 7u8) ;
pub const ATOM_LOCALNAME__69_73_69_6E_64_65_78 : LocalName = LocalName :: pack_inline (8675450682577479936u64 , 7u8) ;
pub const ATOM_LOCALNAME__69_73_6D_61_70 : LocalName = LocalName :: pack_inline (123563750418688u64 , 5u8) ;
pub const ATOM_LOCALNAME__69_74_65_6D_69_64 : LocalName = LocalName :: pack_inline (28263416245545216u64 , 6u8) ;
pub const ATOM_LOCALNAME__69_74_65_6D_72_65_66 : LocalName = LocalName :: pack_inline (7378429378695555328u64 , 7u8) ;
pub const ATOM_LOCALNAME__6B : LocalName = LocalName :: pack_inline (27392u64 , 1u8) ;
pub const ATOM_LOCALNAME__6B_31 : LocalName = LocalName :: pack_inline (3238656u64 , 2u8) ;
pub const ATOM_LOCALNAME__6B_32 : LocalName = LocalName :: pack_inline (3304192u64 , 2u8) ;
pub const ATOM_LOCALNAME__6B_33 : LocalName = LocalName :: pack_inline (3369728u64 , 2u8) ;
pub const ATOM_LOCALNAME__6B_34 : LocalName = LocalName :: pack_inline (3435264u64 , 2u8) ;
pub const ATOM_LOCALNAME__6B_62_64 : LocalName = LocalName :: pack_inline (1684171520u64 , 3u8) ;
pub const ATOM_LOCALNAME__6B_65_72_6E_69_6E_67 : LocalName = LocalName :: pack_inline (7453010356431317760u64 , 7u8) ;
pub const ATOM_LOCALNAME__6B_65_79_67_65_6E : LocalName = LocalName :: pack_inline (31073742530898688u64 , 6u8) ;
pub const ATOM_LOCALNAME__6B_69_6E_64 : LocalName = LocalName :: pack_inline (431349132032u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_61_62_65_6C : LocalName = LocalName :: pack_inline (119182698048512u64 , 5u8) ;
pub const ATOM_LOCALNAME__6C_61_6D_62_64_61 : LocalName = LocalName :: pack_inline (27413446645607424u64 , 6u8) ;
pub const ATOM_LOCALNAME__6C_61_6E_67 : LocalName = LocalName :: pack_inline (444233509888u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_61_72_67_65_6F_70 : LocalName = LocalName :: pack_inline (8101805749637835776u64 , 7u8) ;
pub const ATOM_LOCALNAME__6C_63_6D : LocalName = LocalName :: pack_inline (1835232256u64 , 3u8) ;
pub const ATOM_LOCALNAME__6C_65_67_65_6E_64 : LocalName = LocalName :: pack_inline (28268879476517888u64 , 6u8) ;
pub const ATOM_LOCALNAME__6C_65_71 : LocalName = LocalName :: pack_inline (1902472192u64 , 3u8) ;
pub const ATOM_LOCALNAME__6C_69 : LocalName = LocalName :: pack_inline (6908928u64 , 2u8) ;
pub const ATOM_LOCALNAME__6C_69_6D_69_74 : LocalName = LocalName :: pack_inline (127996156013568u64 , 5u8) ;
pub const ATOM_LOCALNAME__6C_69_6E_65 : LocalName = LocalName :: pack_inline (435644099584u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_69_6E_6B : LocalName = LocalName :: pack_inline (461413903360u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_69_73_74 : LocalName = LocalName :: pack_inline (500152495104u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_69_73_74_69_6E_67 : LocalName = LocalName :: pack_inline (7453010382218161152u64 , 7u8) ;
pub const ATOM_LOCALNAME__6C_6E : LocalName = LocalName :: pack_inline (7236608u64 , 2u8) ;
pub const ATOM_LOCALNAME__6C_6F_63_61_6C : LocalName = LocalName :: pack_inline (119165535874048u64 , 5u8) ;
pub const ATOM_LOCALNAME__6C_6F_67 : LocalName = LocalName :: pack_inline (1735355392u64 , 3u8) ;
pub const ATOM_LOCALNAME__6C_6F_67_62_61_73_65 : LocalName = LocalName :: pack_inline (7310293695422491648u64 , 7u8) ;
pub const ATOM_LOCALNAME__6C_6F_6F_70 : LocalName = LocalName :: pack_inline (482905910272u64 , 4u8) ;
pub const ATOM_LOCALNAME__6C_6F_77 : LocalName = LocalName :: pack_inline (2003790848u64 , 3u8) ;
pub const ATOM_LOCALNAME__6C_6F_77_73_72_63 : LocalName = LocalName :: pack_inline (27991862944951296u64 , 6u8) ;
pub const ATOM_LOCALNAME__6C_71_75_6F_74_65 : LocalName = LocalName :: pack_inline (28556994708335616u64 , 6u8) ;
pub const ATOM_LOCALNAME__6C_73_70_61_63_65 : LocalName = LocalName :: pack_inline (28538242797366272u64 , 6u8) ;
pub const ATOM_LOCALNAME__6C_74 : LocalName = LocalName :: pack_inline (7629824u64 , 2u8) ;
pub const ATOM_LOCALNAME__6D_61_63_72_6F_73 : LocalName = LocalName :: pack_inline (32492159406009600u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_61_63_74_69_6F_6E : LocalName = LocalName :: pack_inline (7957695015191407872u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_61_69_6E : LocalName = LocalName :: pack_inline (474214395136u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_61_70 : LocalName = LocalName :: pack_inline (1885433088u64 , 3u8) ;
pub const ATOM_LOCALNAME__6D_61_72_6B : LocalName = LocalName :: pack_inline (461480488192u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_61_72_6B_65_72 : LocalName = LocalName :: pack_inline (32199659499908352u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_61_72_71_75_65_65 : LocalName = LocalName :: pack_inline (7306375100589239552u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_61_73_6B : LocalName = LocalName :: pack_inline (461497265408u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_61_74_68 : LocalName = LocalName :: pack_inline (448629140736u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_61_74_72_69_78 : LocalName = LocalName :: pack_inline (33892937505008896u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_61_78 : LocalName = LocalName :: pack_inline (2019650816u64 , 3u8) ;
pub const ATOM_LOCALNAME__6D_61_78_73_69_7A_65 : LocalName = LocalName :: pack_inline (7312272889651227904u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_65_61_6E : LocalName = LocalName :: pack_inline (474080439552u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_65_64_69_61 : LocalName = LocalName :: pack_inline (107105283828992u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_65_64_69_61_6E : LocalName = LocalName :: pack_inline (31069352722001152u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_65_6E_75 : LocalName = LocalName :: pack_inline (504363314432u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_65_72_72_6F_72 : LocalName = LocalName :: pack_inline (32210684681219328u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_65_74_61 : LocalName = LocalName :: pack_inline (418564631808u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_65_74_65_72 : LocalName = LocalName :: pack_inline (125780070067456u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_65_74_68_6F_64 : LocalName = LocalName :: pack_inline (28269992091151616u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_66_65_6E_63_65_64 : LocalName = LocalName :: pack_inline (7234297702239333632u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_66_72_61_63 : LocalName = LocalName :: pack_inline (109270182292736u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_67_6C_79_70_68 : LocalName = LocalName :: pack_inline (29397064389979392u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_69 : LocalName = LocalName :: pack_inline (6909184u64 , 2u8) ;
pub const ATOM_LOCALNAME__6D_69_6E : LocalName = LocalName :: pack_inline (1852402944u64 , 3u8) ;
pub const ATOM_LOCALNAME__6D_69_6E_73_69_7A_65 : LocalName = LocalName :: pack_inline (7312272889483980032u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_69_6E_75_73 : LocalName = LocalName :: pack_inline (126948200770816u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_6E : LocalName = LocalName :: pack_inline (7236864u64 , 2u8) ;
pub const ATOM_LOCALNAME__6D_6F : LocalName = LocalName :: pack_inline (7302400u64 , 2u8) ;
pub const ATOM_LOCALNAME__6D_6F_64_65 : LocalName = LocalName :: pack_inline (435476720896u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_6F_6D_65_6E_74 : LocalName = LocalName :: pack_inline (32772479205207296u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_6F_76_65_72 : LocalName = LocalName :: pack_inline (125780104277248u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_70_61_64_64_65_64 : LocalName = LocalName :: pack_inline (7234298758734834944u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_70_61_74_68 : LocalName = LocalName :: pack_inline (114849060252928u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_72_6F_6F_74 : LocalName = LocalName :: pack_inline (128021959961856u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_72_6F_77 : LocalName = LocalName :: pack_inline (512970878208u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_73 : LocalName = LocalName :: pack_inline (7564544u64 , 2u8) ;
pub const ATOM_LOCALNAME__6D_73_70_61_63_65 : LocalName = LocalName :: pack_inline (28538242797366528u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_73_71_72_74 : LocalName = LocalName :: pack_inline (128034878483712u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_73_74_79_6C_65 : LocalName = LocalName :: pack_inline (28548241548340480u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_73_75_62 : LocalName = LocalName :: pack_inline (422877293824u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_73_75_62_73_75_70 : LocalName = LocalName :: pack_inline (8103509971237563648u64 , 7u8) ;
pub const ATOM_LOCALNAME__6D_73_75_70 : LocalName = LocalName :: pack_inline (483006835968u64 , 4u8) ;
pub const ATOM_LOCALNAME__6D_74_61_62_6C_65 : LocalName = LocalName :: pack_inline (28548142445391104u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_74_64 : LocalName = LocalName :: pack_inline (1685351680u64 , 3u8) ;
pub const ATOM_LOCALNAME__6D_74_65_78_74 : LocalName = LocalName :: pack_inline (128060447026432u64 , 5u8) ;
pub const ATOM_LOCALNAME__6D_74_72 : LocalName = LocalName :: pack_inline (1920232704u64 , 3u8) ;
pub const ATOM_LOCALNAME__6D_75_6E_64_65_72 : LocalName = LocalName :: pack_inline (32199629369339136u64 , 6u8) ;
pub const ATOM_LOCALNAME__6D_75_74_65_64 : LocalName = LocalName :: pack_inline (110386908327168u64 , 5u8) ;
pub const ATOM_LOCALNAME__6E_61_6D_65 : LocalName = LocalName :: pack_inline (435626798592u64 , 4u8) ;
pub const ATOM_LOCALNAME__6E_61_72_67_73 : LocalName = LocalName :: pack_inline (126888137813504u64 , 5u8) ;
pub const ATOM_LOCALNAME__6E_61_76 : LocalName = LocalName :: pack_inline (1986096640u64 , 3u8) ;
pub const ATOM_LOCALNAME__6E_65_71 : LocalName = LocalName :: pack_inline (1902472704u64 , 3u8) ;
pub const ATOM_LOCALNAME__6E_65_73_74 : LocalName = LocalName :: pack_inline (500152233472u64 , 4u8) ;
pub const ATOM_LOCALNAME__6E_65_78_74_69_64 : LocalName = LocalName :: pack_inline (28263446628101632u64 , 6u8) ;
pub const ATOM_LOCALNAME__6E_6F_62_72 : LocalName = LocalName :: pack_inline (491277741568u64 , 4u8) ;
pub const ATOM_LOCALNAME__6E_6F_65_6D_62_65_64 : LocalName = LocalName :: pack_inline (7234296598433328640u64 , 7u8) ;
pub const ATOM_LOCALNAME__6E_6F_68_72_65_66 : LocalName = LocalName :: pack_inline (28821989677297152u64 , 6u8) ;
pub const ATOM_LOCALNAME__6E_6F_6E_63_65 : LocalName = LocalName :: pack_inline (111477728964096u64 , 5u8) ;
pub const ATOM_LOCALNAME__6E_6F_6E_65 : LocalName = LocalName :: pack_inline (435644493312u64 , 4u8) ;
pub const ATOM_LOCALNAME__6E_6F_73_68_61_64_65 : LocalName = LocalName :: pack_inline (7306071596742962688u64 , 7u8) ;
pub const ATOM_LOCALNAME__6E_6F_74 : LocalName = LocalName :: pack_inline (1953459712u64 , 3u8) ;
pub const ATOM_LOCALNAME__6E_6F_74_69_6E : LocalName = LocalName :: pack_inline (121399204081152u64 , 5u8) ;
pub const ATOM_LOCALNAME__6E_6F_77_72_61_70 : LocalName = LocalName :: pack_inline (31632341649550848u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_62_6A_65_63_74 : LocalName = LocalName :: pack_inline (32760384526118656u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_66_66_73_65_74 : LocalName = LocalName :: pack_inline (32762643612069632u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6C : LocalName = LocalName :: pack_inline (7106304u64 , 2u8) ;
pub const ATOM_LOCALNAME__6F_6E_61_62_6F_72_74 : LocalName = LocalName :: pack_inline (8390891524076760832u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_62_65_67_69_6E : LocalName = LocalName :: pack_inline (7956003901867454208u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_62_6C_75_72 : LocalName = LocalName :: pack_inline (32217255713337088u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_63_6C_69_63_6B : LocalName = LocalName :: pack_inline (7738144498998210304u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_63_6F_70_79 : LocalName = LocalName :: pack_inline (34182095893851904u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_63_75_74 : LocalName = LocalName :: pack_inline (128047528177408u64 , 5u8) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_61_67 : LocalName = LocalName :: pack_inline (29099066540322560u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_64_72_6F_70 : LocalName = LocalName :: pack_inline (31647734493507328u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_65_6E_64 : LocalName = LocalName :: pack_inline (110425310916352u64 , 5u8) ;
pub const ATOM_LOCALNAME__6F_6E_65_72_72_6F_72 : LocalName = LocalName :: pack_inline (8245935278392241920u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_66_6F_63_75_73 : LocalName = LocalName :: pack_inline (8319665216747892480u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_68_65_6C_70 : LocalName = LocalName :: pack_inline (31644380191158016u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_69_6E_70_75_74 : LocalName = LocalName :: pack_inline (8391737100192345856u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_6B_65_79_75_70 : LocalName = LocalName :: pack_inline (8103516581024132864u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_6C_6F_61_64 : LocalName = LocalName :: pack_inline (28254628859506432u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_6D_6F_76_65 : LocalName = LocalName :: pack_inline (28559193597177600u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_70_61_73_74_65 : LocalName = LocalName :: pack_inline (7310594957464465152u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_72_65_73_65_74 : LocalName = LocalName :: pack_inline (8387236760596147968u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_61_72_74 : LocalName = LocalName :: pack_inline (8390876208525373184u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_6E_73_74_6F_70 : LocalName = LocalName :: pack_inline (31647743335100160u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_6E_7A_6F_6F_6D : LocalName = LocalName :: pack_inline (30803297047572224u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_70_61_63_69_74_79 : LocalName = LocalName :: pack_inline (8751735851445153536u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_70_65_6E : LocalName = LocalName :: pack_inline (474148269824u64 , 4u8) ;
pub const ATOM_LOCALNAME__6F_70_74_69_6D_75_6D : LocalName = LocalName :: pack_inline (7887330622101810944u64 , 7u8) ;
pub const ATOM_LOCALNAME__6F_70_74_69_6F_6E : LocalName = LocalName :: pack_inline (31084746153946880u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_72 : LocalName = LocalName :: pack_inline (7499520u64 , 2u8) ;
pub const ATOM_LOCALNAME__6F_72_64_65_72 : LocalName = LocalName :: pack_inline (125779802484480u64 , 5u8) ;
pub const ATOM_LOCALNAME__6F_72_69_65_6E_74 : LocalName = LocalName :: pack_inline (32772479138295552u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_72_69_67_69_6E : LocalName = LocalName :: pack_inline (31078140309827328u64 , 6u8) ;
pub const ATOM_LOCALNAME__6F_74_68_65_72 : LocalName = LocalName :: pack_inline (125779869724416u64 , 5u8) ;
pub const ATOM_LOCALNAME__6F_75_74_70_75_74 : LocalName = LocalName :: pack_inline (32780223149076224u64 , 6u8) ;
pub const ATOM_LOCALNAME__70 : LocalName = LocalName :: pack_inline (28672u64 , 1u8) ;
pub const ATOM_LOCALNAME__70_61_72_61_6D : LocalName = LocalName :: pack_inline (120265298243584u64 , 5u8) ;
pub const ATOM_LOCALNAME__70_61_72_73_65 : LocalName = LocalName :: pack_inline (111546514632704u64 , 5u8) ;
pub const ATOM_LOCALNAME__70_61_74_68 : LocalName = LocalName :: pack_inline (448629141504u64 , 4u8) ;
pub const ATOM_LOCALNAME__70_61_74_74_65_72_6E : LocalName = LocalName :: pack_inline (7958535042360242176u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_69 : LocalName = LocalName :: pack_inline (6909952u64 , 2u8) ;
pub const ATOM_LOCALNAME__70_69_63_74_75_72_65 : LocalName = LocalName :: pack_inline (7310034287920246784u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_69_65_63_65 : LocalName = LocalName :: pack_inline (111477577576448u64 , 5u8) ;
pub const ATOM_LOCALNAME__70_69_6E_67 : LocalName = LocalName :: pack_inline (444234035200u64 , 4u8) ;
pub const ATOM_LOCALNAME__70_6C_75_73 : LocalName = LocalName :: pack_inline (495891279872u64 , 4u8) ;
pub const ATOM_LOCALNAME__70_6F_69_6E_74_73 : LocalName = LocalName :: pack_inline (32497639885860864u64 , 6u8) ;
pub const ATOM_LOCALNAME__70_6F_6C_79_67_6F_6E : LocalName = LocalName :: pack_inline (7957692837794902016u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_6F_73_74_65_72 : LocalName = LocalName :: pack_inline (32199698172309504u64 , 6u8) ;
pub const ATOM_LOCALNAME__70_6F_77_65_72 : LocalName = LocalName :: pack_inline (125780121055232u64 , 5u8) ;
pub const ATOM_LOCALNAME__70_72_65 : LocalName = LocalName :: pack_inline (1701998592u64 , 3u8) ;
pub const ATOM_LOCALNAME__70_72_65_6C_6F_61_64 : LocalName = LocalName :: pack_inline (7233184987882876928u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_72_69_6D_65_73 : LocalName = LocalName :: pack_inline (32481142916673536u64 , 6u8) ;
pub const ATOM_LOCALNAME__70_72_6F_64_75_63_74 : LocalName = LocalName :: pack_inline (8386676005320945664u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_72_6F_66_69_6C_65 : LocalName = LocalName :: pack_inline (7308332183992823808u64 , 7u8) ;
pub const ATOM_LOCALNAME__70_72_6F_6D_70_74 : LocalName = LocalName :: pack_inline (32774712621953024u64 , 6u8) ;
pub const ATOM_LOCALNAME__71 : LocalName = LocalName :: pack_inline (28928u64 , 1u8) ;
pub const ATOM_LOCALNAME__72 : LocalName = LocalName :: pack_inline (29184u64 , 1u8) ;
pub const ATOM_LOCALNAME__72_61_64_69_75_73 : LocalName = LocalName :: pack_inline (32498717837849088u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_62 : LocalName = LocalName :: pack_inline (6451712u64 , 2u8) ;
pub const ATOM_LOCALNAME__72_65_61_6C : LocalName = LocalName :: pack_inline (465490506240u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_61_6C_73 : LocalName = LocalName :: pack_inline (126909327700480u64 , 5u8) ;
pub const ATOM_LOCALNAME__72_65_63_74 : LocalName = LocalName :: pack_inline (499883799040u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_66_58 : LocalName = LocalName :: pack_inline (379675046400u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_66_59 : LocalName = LocalName :: pack_inline (383970013696u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_66_78 : LocalName = LocalName :: pack_inline (517113999872u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_66_79 : LocalName = LocalName :: pack_inline (521408967168u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_6C : LocalName = LocalName :: pack_inline (1818587648u64 , 3u8) ;
pub const ATOM_LOCALNAME__72_65_6C_6E : LocalName = LocalName :: pack_inline (474264990208u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_65_6D : LocalName = LocalName :: pack_inline (1835364864u64 , 3u8) ;
pub const ATOM_LOCALNAME__72_65_70_65_61_74 : LocalName = LocalName :: pack_inline (32758185603723776u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_65_70_6C_61_63_65 : LocalName = LocalName :: pack_inline (7305790138895135232u64 , 7u8) ;
pub const ATOM_LOCALNAME__72_65_73_74_61_72_74 : LocalName = LocalName :: pack_inline (8390876208524784128u64 , 7u8) ;
pub const ATOM_LOCALNAME__72_65_73_75_6C_74 : LocalName = LocalName :: pack_inline (32770349001437696u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_65_76 : LocalName = LocalName :: pack_inline (1986359808u64 , 3u8) ;
pub const ATOM_LOCALNAME__72_6F_6C_65 : LocalName = LocalName :: pack_inline (435610939904u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_6F_6F_74 : LocalName = LocalName :: pack_inline (500085780992u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_6F_74_61_74_65 : LocalName = LocalName :: pack_inline (28556934561886720u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_6F_77_73 : LocalName = LocalName :: pack_inline (495925031424u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_6F_77_73_70_61_6E : LocalName = LocalName :: pack_inline (7953762058140348928u64 , 7u8) ;
pub const ATOM_LOCALNAME__72_70 : LocalName = LocalName :: pack_inline (7369216u64 , 2u8) ;
pub const ATOM_LOCALNAME__72_71_75_6F_74_65 : LocalName = LocalName :: pack_inline (28556994708337152u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_73_70_61_63_65 : LocalName = LocalName :: pack_inline (28538242797367808u64 , 6u8) ;
pub const ATOM_LOCALNAME__72_74 : LocalName = LocalName :: pack_inline (7631360u64 , 2u8) ;
pub const ATOM_LOCALNAME__72_74_63 : LocalName = LocalName :: pack_inline (1668575744u64 , 3u8) ;
pub const ATOM_LOCALNAME__72_75_62_79 : LocalName = LocalName :: pack_inline (521342906880u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_75_6C_65 : LocalName = LocalName :: pack_inline (435611333120u64 , 4u8) ;
pub const ATOM_LOCALNAME__72_75_6C_65_73 : LocalName = LocalName :: pack_inline (126879448527360u64 , 5u8) ;
pub const ATOM_LOCALNAME__72_78 : LocalName = LocalName :: pack_inline (7893504u64 , 2u8) ;
pub const ATOM_LOCALNAME__72_79 : LocalName = LocalName :: pack_inline (7959040u64 , 2u8) ;
pub const ATOM_LOCALNAME__73 : LocalName = LocalName :: pack_inline (29440u64 , 1u8) ;
pub const ATOM_LOCALNAME__73_61_6D_70 : LocalName = LocalName :: pack_inline (482871440128u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_61_6E_64_62_6F_78 : LocalName = LocalName :: pack_inline (8678263190454366976u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_63_61_6C_65 : LocalName = LocalName :: pack_inline (111516164780800u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_63_68_65_6D_65 : LocalName = LocalName :: pack_inline (28549254958248704u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_63_6F_70_65 : LocalName = LocalName :: pack_inline (111533579531008u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_63_6F_70_65_64 : LocalName = LocalName :: pack_inline (28259031250596608u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_63_72_69_70_74 : LocalName = LocalName :: pack_inline (32774695491433216u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_64_65_76 : LocalName = LocalName :: pack_inline (508507222784u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_65_63 : LocalName = LocalName :: pack_inline (1667592960u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_65_63_68 : LocalName = LocalName :: pack_inline (448344191744u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_65_63_74_69_6F_6E : LocalName = LocalName :: pack_inline (7957695015191671552u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_65_65_64 : LocalName = LocalName :: pack_inline (431197876992u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_65_6C_65_63_74 : LocalName = LocalName :: pack_inline (32760384559870720u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_65_70 : LocalName = LocalName :: pack_inline (1885696768u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_65_74 : LocalName = LocalName :: pack_inline (1952805632u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_65_74_64_69_66_66 : LocalName = LocalName :: pack_inline (7378700919663588096u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_68_61_70_65 : LocalName = LocalName :: pack_inline (111533344977664u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_68_6F_77 : LocalName = LocalName :: pack_inline (512970224384u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_69_6E : LocalName = LocalName :: pack_inline (1852404480u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_69_6E_68 : LocalName = LocalName :: pack_inline (448529003264u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_69_7A_65 : LocalName = LocalName :: pack_inline (435845427968u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_69_7A_65_73 : LocalName = LocalName :: pack_inline (126879682622208u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_6C_6F_70_65 : LocalName = LocalName :: pack_inline (111533580120832u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_6C_6F_74 : LocalName = LocalName :: pack_inline (500085584640u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_6D_61_6C_6C : LocalName = LocalName :: pack_inline (119212746830592u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_6F_75_72_63_65 : LocalName = LocalName :: pack_inline (28538315895436032u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_70_61_63_65 : LocalName = LocalName :: pack_inline (111477510927104u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_70_61_63_65_72 : LocalName = LocalName :: pack_inline (32199624855941888u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_70_61_63_69_6E_67 : LocalName = LocalName :: pack_inline (7453010308902187776u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_70_61_6E : LocalName = LocalName :: pack_inline (474081161984u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_70_65_65_64 : LocalName = LocalName :: pack_inline (110386656342784u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_72_63 : LocalName = LocalName :: pack_inline (1668444928u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_72_63_64_6F_63 : LocalName = LocalName :: pack_inline (27988499650212608u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_72_63_6C_61_6E_67 : LocalName = LocalName :: pack_inline (7453001551497556736u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_72_63_73_65_74 : LocalName = LocalName :: pack_inline (32762643562525440u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_74_61_6E_64_62_79 : LocalName = LocalName :: pack_inline (8746663851551126272u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_74_61_72_74 : LocalName = LocalName :: pack_inline (128034610115328u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_74_65_6D_68 : LocalName = LocalName :: pack_inline (114819062854400u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_74_65_6D_76 : LocalName = LocalName :: pack_inline (130212225643264u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_74_65_70 : LocalName = LocalName :: pack_inline (482738467584u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_74_6F_70 : LocalName = LocalName :: pack_inline (482906239744u64 , 4u8) ;
pub const ATOM_LOCALNAME__73_74_72_69_6B_65 : LocalName = LocalName :: pack_inline (28547073283748608u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_74_72_69_6E_67 : LocalName = LocalName :: pack_inline (29113321772053248u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6B_65 : LocalName = LocalName :: pack_inline (28547099053552384u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_74_72_6F_6E_67 : LocalName = LocalName :: pack_inline (29113347541857024u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_74_79_6C_65 : LocalName = LocalName :: pack_inline (111516568548096u64 , 5u8) ;
pub const ATOM_LOCALNAME__73_75_62 : LocalName = LocalName :: pack_inline (1651864320u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_75_62_73_65_74 : LocalName = LocalName :: pack_inline (32762643545944832u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_75_6D : LocalName = LocalName :: pack_inline (1836413696u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_75_6D_6D_61_72_79 : LocalName = LocalName :: pack_inline (8751164148550038272u64 , 7u8) ;
pub const ATOM_LOCALNAME__73_75_70 : LocalName = LocalName :: pack_inline (1886745344u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_76_67 : LocalName = LocalName :: pack_inline (1735815936u64 , 3u8) ;
pub const ATOM_LOCALNAME__73_77_69_74_63_68 : LocalName = LocalName :: pack_inline (29382749214700288u64 , 6u8) ;
pub const ATOM_LOCALNAME__73_79_6D_62_6F_6C : LocalName = LocalName :: pack_inline (30521766018904832u64 , 6u8) ;
pub const ATOM_LOCALNAME__74_61_62_6C_65 : LocalName = LocalName :: pack_inline (111516181427200u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_61_6E : LocalName = LocalName :: pack_inline (1851880448u64 , 3u8) ;
pub const ATOM_LOCALNAME__74_61_6E_68 : LocalName = LocalName :: pack_inline (448528479232u64 , 4u8) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74 : LocalName = LocalName :: pack_inline (32762592273462272u64 , 6u8) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_58 : LocalName = LocalName :: pack_inline (6373830867611120640u64 , 7u8) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_59 : LocalName = LocalName :: pack_inline (6445888461649048576u64 , 7u8) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_78 : LocalName = LocalName :: pack_inline (8679673876824814592u64 , 7u8) ;
pub const ATOM_LOCALNAME__74_61_72_67_65_74_79 : LocalName = LocalName :: pack_inline (8751731470862742528u64 , 7u8) ;
pub const ATOM_LOCALNAME__74_62_6F_64_79 : LocalName = LocalName :: pack_inline (133472272413696u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_62_72_65_61_6B : LocalName = LocalName :: pack_inline (30224910846686208u64 , 6u8) ;
pub const ATOM_LOCALNAME__74_64 : LocalName = LocalName :: pack_inline (6583296u64 , 2u8) ;
pub const ATOM_LOCALNAME__74_65_6E_64_73_74_6F : LocalName = LocalName :: pack_inline (8031170910694503424u64 , 7u8) ;
pub const ATOM_LOCALNAME__74_65_78_74 : LocalName = LocalName :: pack_inline (500236121088u64 , 4u8) ;
pub const ATOM_LOCALNAME__74_66_6F_6F_74 : LocalName = LocalName :: pack_inline (128021959177216u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_68 : LocalName = LocalName :: pack_inline (6845440u64 , 2u8) ;
pub const ATOM_LOCALNAME__74_68_65_61_64 : LocalName = LocalName :: pack_inline (110369475949568u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_69_6D_65 : LocalName = LocalName :: pack_inline (435627324416u64 , 4u8) ;
pub const ATOM_LOCALNAME__74_69_6D_65_73 : LocalName = LocalName :: pack_inline (126879464518656u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_69_74_6C_65 : LocalName = LocalName :: pack_inline (111516483941376u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_6F : LocalName = LocalName :: pack_inline (7304192u64 , 2u8) ;
pub const ATOM_LOCALNAME__74_6F_67_67_6C_65 : LocalName = LocalName :: pack_inline (28548164020564992u64 , 6u8) ;
pub const ATOM_LOCALNAME__74_72 : LocalName = LocalName :: pack_inline (7500800u64 , 2u8) ;
pub const ATOM_LOCALNAME__74_72_61_63_6B : LocalName = LocalName :: pack_inline (118074580825088u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_72_65_66 : LocalName = LocalName :: pack_inline (439788663808u64 , 4u8) ;
pub const ATOM_LOCALNAME__74_72_75_65 : LocalName = LocalName :: pack_inline (435762131968u64 , 4u8) ;
pub const ATOM_LOCALNAME__74_73_70_61_6E : LocalName = LocalName :: pack_inline (121364777497600u64 , 5u8) ;
pub const ATOM_LOCALNAME__74_74 : LocalName = LocalName :: pack_inline (7631872u64 , 2u8) ;
pub const ATOM_LOCALNAME__74_79_70_65 : LocalName = LocalName :: pack_inline (435678704640u64 , 4u8) ;
pub const ATOM_LOCALNAME__75 : LocalName = LocalName :: pack_inline (29952u64 , 1u8) ;
pub const ATOM_LOCALNAME__75_31 : LocalName = LocalName :: pack_inline (3241216u64 , 2u8) ;
pub const ATOM_LOCALNAME__75_32 : LocalName = LocalName :: pack_inline (3306752u64 , 2u8) ;
pub const ATOM_LOCALNAME__75_6C : LocalName = LocalName :: pack_inline (7107840u64 , 2u8) ;
pub const ATOM_LOCALNAME__75_6E_69_63_6F_64_65 : LocalName = LocalName :: pack_inline (7306086968263079168u64 , 7u8) ;
pub const ATOM_LOCALNAME__75_6E_69_6F_6E : LocalName = LocalName :: pack_inline (121424789271808u64 , 5u8) ;
pub const ATOM_LOCALNAME__75_70_6C_69_6D_69_74 : LocalName = LocalName :: pack_inline (8388356080512562432u64 , 7u8) ;
pub const ATOM_LOCALNAME__75_73_65 : LocalName = LocalName :: pack_inline (1702065408u64 , 3u8) ;
pub const ATOM_LOCALNAME__75_73_65_6D_61_70 : LocalName = LocalName :: pack_inline (31632319872988416u64 , 6u8) ;
pub const ATOM_LOCALNAME__76_61_6C_69_67_6E : LocalName = LocalName :: pack_inline (31075949925725696u64 , 6u8) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65 : LocalName = LocalName :: pack_inline (111555003905536u64 , 5u8) ;
pub const ATOM_LOCALNAME__76_61_6C_75_65_73 : LocalName = LocalName :: pack_inline (32481177325630976u64 , 6u8) ;
pub const ATOM_LOCALNAME__76_61_72 : LocalName = LocalName :: pack_inline (1918989824u64 , 3u8) ;
pub const ATOM_LOCALNAME__76_65_63_74_6F_72 : LocalName = LocalName :: pack_inline (32210693019497984u64 , 6u8) ;
pub const ATOM_LOCALNAME__76_65_72_73_69_6F_6E : LocalName = LocalName :: pack_inline (7957695011148363264u64 , 7u8) ;
pub const ATOM_LOCALNAME__76_69_64_65_6F : LocalName = LocalName :: pack_inline (122481267013120u64 , 5u8) ;
pub const ATOM_LOCALNAME__76_69_65_77 : LocalName = LocalName :: pack_inline (512802518528u64 , 4u8) ;
pub const ATOM_LOCALNAME__76_69_65_77_42_6F_78 : LocalName = LocalName :: pack_inline (8678228087536186880u64 , 7u8) ;
pub const ATOM_LOCALNAME__76_69_65_77_62_6F_78 : LocalName = LocalName :: pack_inline (8678263271908275712u64 , 7u8) ;
pub const ATOM_LOCALNAME__76_6B_65_72_6E : LocalName = LocalName :: pack_inline (121437606868480u64 , 5u8) ;
pub const ATOM_LOCALNAME__76_6C_69_6E_6B : LocalName = LocalName :: pack_inline (118121959290368u64 , 5u8) ;
pub const ATOM_LOCALNAME__76_73_70_61_63_65 : LocalName = LocalName :: pack_inline (28538242797368832u64 , 6u8) ;
pub const ATOM_LOCALNAME__77_62_72 : LocalName = LocalName :: pack_inline (1919055616u64 , 3u8) ;
pub const ATOM_LOCALNAME__77_68_65_6E : LocalName = LocalName :: pack_inline (474147747584u64 , 4u8) ;
pub const ATOM_LOCALNAME__77_69_64_74_68 : LocalName = LocalName :: pack_inline (114849110128384u64 , 5u8) ;
pub const ATOM_LOCALNAME__77_69_64_74_68_73 : LocalName = LocalName :: pack_inline (32484471431853824u64 , 6u8) ;
pub const ATOM_LOCALNAME__77_72_61_70 : LocalName = LocalName :: pack_inline (482671228672u64 , 4u8) ;
pub const ATOM_LOCALNAME__78 : LocalName = LocalName :: pack_inline (30720u64 , 1u8) ;
pub const ATOM_LOCALNAME__78_31 : LocalName = LocalName :: pack_inline (3241984u64 , 2u8) ;
pub const ATOM_LOCALNAME__78_32 : LocalName = LocalName :: pack_inline (3307520u64 , 2u8) ;
pub const ATOM_LOCALNAME__78_6C_69_6E_6B : LocalName = LocalName :: pack_inline (118121959290880u64 , 5u8) ;
pub const ATOM_LOCALNAME__78_6D_6C_6E_73 : LocalName = LocalName :: pack_inline (126918102710272u64 , 5u8) ;
pub const ATOM_LOCALNAME__78_6D_70 : LocalName = LocalName :: pack_inline (1886222336u64 , 3u8) ;
pub const ATOM_LOCALNAME__78_6F_72 : LocalName = LocalName :: pack_inline (1919907840u64 , 3u8) ;
pub const ATOM_LOCALNAME__78_72_65_66 : LocalName = LocalName :: pack_inline (439788664832u64 , 4u8) ;
pub const ATOM_LOCALNAME__79 : LocalName = LocalName :: pack_inline (30976u64 , 1u8) ;
pub const ATOM_LOCALNAME__79_31 : LocalName = LocalName :: pack_inline (3242240u64 , 2u8) ;
pub const ATOM_LOCALNAME__79_32 : LocalName = LocalName :: pack_inline (3307776u64 , 2u8) ;
pub const ATOM_LOCALNAME__7A : LocalName = LocalName :: pack_inline (31232u64 , 1u8) ;
# [doc = "Takes a local name as a string and returns its key in the string cache."] # [macro_export] macro_rules ! local_name { ("patterncontentunits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_63_6F_6E_74_65_6E_74_75_6E_69_74_73 } ;
("aria-sort") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_6F_72_74 } ;
("onbeforeprint") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_72_69_6E_74 } ;
("thickmathspace") => { $ crate :: ATOM_LOCALNAME__74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("overflow") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_66_6C_6F_77 } ;
("feoffset") => { $ crate :: ATOM_LOCALNAME__66_65_6F_66_66_73_65_74 } ;
("hidefocus") => { $ crate :: ATOM_LOCALNAME__68_69_64_65_66_6F_63_75_73 } ;
("stitchtiles") => { $ crate :: ATOM_LOCALNAME__73_74_69_74_63_68_74_69_6C_65_73 } ;
("radialgradient") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_61_6C_67_72_61_64_69_65_6E_74 } ;
("ondataavailable") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_61_76_61_69_6C_61_62_6C_65 } ;
("altGlyphDef") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_44_65_66 } ;
("onbeforeeditfocus") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_65_64_69_74_66_6F_63_75_73 } ;
("prefetch") => { $ crate :: ATOM_LOCALNAME__70_72_65_66_65_74_63_68 } ;
("vert-origin-y") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_79 } ;
("subscriptshift") => { $ crate :: ATOM_LOCALNAME__73_75_62_73_63_72_69_70_74_73_68_69_66_74 } ;
("stop-opacity") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70_2D_6F_70_61_63_69_74_79 } ;
("aria-hidden") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_68_69_64_64_65_6E } ;
("intercept") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_63_65_70_74 } ;
("animatecolor") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_63_6F_6C_6F_72 } ;
("maskContentUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_43_6F_6E_74_65_6E_74_55_6E_69_74_73 } ;
("onfocusout") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_6F_75_74 } ;
("repeat-max") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_61_78 } ;
("aria-owns") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6F_77_6E_73 } ;
("encoding") => { $ crate :: ATOM_LOCALNAME__65_6E_63_6F_64_69_6E_67 } ;
("aria-required") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_71_75_69_72_65_64 } ;
("surfacescale") => { $ crate :: ATOM_LOCALNAME__73_75_72_66_61_63_65_73_63_61_6C_65 } ;
("irrelevant") => { $ crate :: ATOM_LOCALNAME__69_72_72_65_6C_65_76_61_6E_74 } ;
("feColorMatrix") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6C_6F_72_4D_61_74_72_69_78 } ;
("altGlyph") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68 } ;
("systemlanguage") => { $ crate :: ATOM_LOCALNAME__73_79_73_74_65_6D_6C_61_6E_67_75_61_67_65 } ;
("ononline") => { $ crate :: ATOM_LOCALNAME__6F_6E_6F_6E_6C_69_6E_65 } ;
("fespecularlighting") => { $ crate :: ATOM_LOCALNAME__66_65_73_70_65_63_75_6C_61_72_6C_69_67_68_74_69_6E_67 } ;
("glyphref") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_72_65_66 } ;
("arabic-form") => { $ crate :: ATOM_LOCALNAME__61_72_61_62_69_63_2D_66_6F_72_6D } ;
("pointsatz") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_7A } ;
("notsubset") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_73_75_62_73_65_74 } ;
("clip-rule") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_2D_72_75_6C_65 } ;
("numOctaves") => { $ crate :: ATOM_LOCALNAME__6E_75_6D_4F_63_74_61_76_65_73 } ;
("accept-charset") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_70_74_2D_63_68_61_72_73_65_74 } ;
("maxlength") => { $ crate :: ATOM_LOCALNAME__6D_61_78_6C_65_6E_67_74_68 } ;
("aria-atomic") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_74_6F_6D_69_63 } ;
("feSpotLight") => { $ crate :: ATOM_LOCALNAME__66_65_53_70_6F_74_4C_69_67_68_74 } ;
("verythinmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("spreadMethod") => { $ crate :: ATOM_LOCALNAME__73_70_72_65_61_64_4D_65_74_68_6F_64 } ;
("crossorigin") => { $ crate :: ATOM_LOCALNAME__63_72_6F_73_73_6F_72_69_67_69_6E } ;
("aria-selected") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_6C_65_63_74_65_64 } ;
("onrowexit") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_65_78_69_74 } ;
("cap-height") => { $ crate :: ATOM_LOCALNAME__63_61_70_2D_68_65_69_67_68_74 } ;
("fedropshadow") => { $ crate :: ATOM_LOCALNAME__66_65_64_72_6F_70_73_68_61_64_6F_77 } ;
("datatemplate") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_74_65_6D_70_6C_61_74_65 } ;
("contenteditable") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_65_64_69_74_61_62_6C_65 } ;
("edgeMode") => { $ crate :: ATOM_LOCALNAME__65_64_67_65_4D_6F_64_65 } ;
("v-mathematical") => { $ crate :: ATOM_LOCALNAME__76_2D_6D_61_74_68_65_6D_61_74_69_63_61_6C } ;
("units-per-em") => { $ crate :: ATOM_LOCALNAME__75_6E_69_74_73_2D_70_65_72_2D_65_6D } ;
("feComposite") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_73_69_74_65 } ;
("fontfamily") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_66_61_6D_69_6C_79 } ;
("itemscope") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_73_63_6F_70_65 } ;
("color-profile") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_70_72_6F_66_69_6C_65 } ;
("hreflang") => { $ crate :: ATOM_LOCALNAME__68_72_65_66_6C_61_6E_67 } ;
("contentscripttype") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_63_72_69_70_74_74_79_70_65 } ;
("condition") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_64_69_74_69_6F_6E } ;
("onreadystatechange") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_61_64_79_73_74_61_74_65_63_68_61_6E_67_65 } ;
("notprsubset") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_70_72_73_75_62_73_65_74 } ;
("selected") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_65_64 } ;
("onrowenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_65_6E_74_65_72 } ;
("stroke-width") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_77_69_64_74_68 } ;
("repeat-start") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_73_74_61_72_74 } ;
("mphantom") => { $ crate :: ATOM_LOCALNAME__6D_70_68_61_6E_74_6F_6D } ;
("onmouseover") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_76_65_72 } ;
("translate") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_6C_61_74_65 } ;
("contentStyleType") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_74_79_6C_65_54_79_70_65 } ;
("font-face-name") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_6E_61_6D_65 } ;
("textpath") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_70_61_74_68 } ;
("ondragover") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_6F_76_65_72 } ;
("requiredExtensions") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_45_78_74_65_6E_73_69_6F_6E_73 } ;
("font-size") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65 } ;
("tabindex") => { $ crate :: ATOM_LOCALNAME__74_61_62_69_6E_64_65_78 } ;
("aria-readonly") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_61_64_6F_6E_6C_79 } ;
("alphabetic") => { $ crate :: ATOM_LOCALNAME__61_6C_70_68_61_62_65_74_69_63 } ;
("femorphology") => { $ crate :: ATOM_LOCALNAME__66_65_6D_6F_72_70_68_6F_6C_6F_67_79 } ;
("language") => { $ crate :: ATOM_LOCALNAME__6C_61_6E_67_75_61_67_65 } ;
("maskunits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_75_6E_69_74_73 } ;
("zoomandpan") => { $ crate :: ATOM_LOCALNAME__7A_6F_6F_6D_61_6E_64_70_61_6E } ;
("controllerchange") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_6C_65_72_63_68_61_6E_67_65 } ;
("stroke-dasharray") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_61_72_72_61_79 } ;
("onbeforecopy") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_6F_70_79 } ;
("feSpecularLighting") => { $ crate :: ATOM_LOCALNAME__66_65_53_70_65_63_75_6C_61_72_4C_69_67_68_74_69_6E_67 } ;
("feDistantLight") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_73_74_61_6E_74_4C_69_67_68_74 } ;
("repeat-template") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_74_65_6D_70_6C_61_74_65 } ;
("pointsAtX") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_58 } ;
("marker-start") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_73_74_61_72_74 } ;
("repeatDur") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_44_75_72 } ;
("xml:lang") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_6C_61_6E_67 } ;
("scriptsizemultiplier") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_73_69_7A_65_6D_75_6C_74_69_70_6C_69_65_72 } ;
("v-hanging") => { $ crate :: ATOM_LOCALNAME__76_2D_68_61_6E_67_69_6E_67 } ;
("groupalign") => { $ crate :: ATOM_LOCALNAME__67_72_6F_75_70_61_6C_69_67_6E } ;
("shape-rendering") => { $ crate :: ATOM_LOCALNAME__73_68_61_70_65_2D_72_65_6E_64_65_72_69_6E_67 } ;
("letter-spacing") => { $ crate :: ATOM_LOCALNAME__6C_65_74_74_65_72_2D_73_70_61_63_69_6E_67 } ;
("flood-color") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_64_2D_63_6F_6C_6F_72 } ;
("patternUnits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_55_6E_69_74_73 } ;
("onoffline") => { $ crate :: ATOM_LOCALNAME__6F_6E_6F_66_66_6C_69_6E_65 } ;
("keySplines") => { $ crate :: ATOM_LOCALNAME__6B_65_79_53_70_6C_69_6E_65_73 } ;
("feConvolveMatrix") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6E_76_6F_6C_76_65_4D_61_74_72_69_78 } ;
("direction") => { $ crate :: ATOM_LOCALNAME__64_69_72_65_63_74_69_6F_6E } ;
("filterRes") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_52_65_73 } ;
("onmousedown") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_64_6F_77_6E } ;
("plaintext") => { $ crate :: ATOM_LOCALNAME__70_6C_61_69_6E_74_65_78_74 } ;
("longdesc") => { $ crate :: ATOM_LOCALNAME__6C_6F_6E_67_64_65_73_63 } ;
("kernelmatrix") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_6D_61_74_72_69_78 } ;
("spellcheck") => { $ crate :: ATOM_LOCALNAME__73_70_65_6C_6C_63_68_65_63_6B } ;
("onafterprint") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_66_74_65_72_70_72_69_6E_74 } ;
("scalarproduct") => { $ crate :: ATOM_LOCALNAME__73_63_61_6C_61_72_70_72_6F_64_75_63_74 } ;
("rendering-intent") => { $ crate :: ATOM_LOCALNAME__72_65_6E_64_65_72_69_6E_67_2D_69_6E_74_65_6E_74 } ;
("elevation") => { $ crate :: ATOM_LOCALNAME__65_6C_65_76_61_74_69_6F_6E } ;
("stddeviation") => { $ crate :: ATOM_LOCALNAME__73_74_64_64_65_76_69_61_74_69_6F_6E } ;
("columnspacing") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_63_69_6E_67 } ;
("stroke-dashoffset") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_64_61_73_68_6F_66_66_73_65_74 } ;
("datalist") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_6C_69_73_74 } ;
("animatemotion") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_6D_6F_74_69_6F_6E } ;
("infinity") => { $ crate :: ATOM_LOCALNAME__69_6E_66_69_6E_69_74_79 } ;
("aria-live") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_69_76_65 } ;
("font-family") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_6D_69_6C_79 } ;
("piecewise") => { $ crate :: ATOM_LOCALNAME__70_69_65_63_65_77_69_73_65 } ;
("stroke-linejoin") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_6A_6F_69_6E } ;
("fecomposite") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_73_69_74_65 } ;
("aria-datatype") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_61_74_61_74_79_70_65 } ;
("feDiffuseLighting") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_66_66_75_73_65_4C_69_67_68_74_69_6E_67 } ;
("cellspacing") => { $ crate :: ATOM_LOCALNAME__63_65_6C_6C_73_70_61_63_69_6E_67 } ;
("metadata") => { $ crate :: ATOM_LOCALNAME__6D_65_74_61_64_61_74_61 } ;
("keysplines") => { $ crate :: ATOM_LOCALNAME__6B_65_79_73_70_6C_69_6E_65_73 } ;
("foreignobject") => { $ crate :: ATOM_LOCALNAME__66_6F_72_65_69_67_6E_6F_62_6A_65_63_74 } ;
("reversed") => { $ crate :: ATOM_LOCALNAME__72_65_76_65_72_73_65_64 } ;
("mozbrowser") => { $ crate :: ATOM_LOCALNAME__6D_6F_7A_62_72_6F_77_73_65_72 } ;
("externalResourcesRequired") => { $ crate :: ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_52_65_73_6F_75_72_63_65_73_52_65_71_75_69_72_65_64 } ;
("animatetransform") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_74_72_61_6E_73_66_6F_72_6D } ;
("markerunits") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_75_6E_69_74_73 } ;
("text-decoration") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_64_65_63_6F_72_61_74_69_6F_6E } ;
("xchannelselector") => { $ crate :: ATOM_LOCALNAME__78_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 } ;
("onpopstate") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_6F_70_73_74_61_74_65 } ;
("pointsAtY") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_59 } ;
("color-rendering") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_72_65_6E_64_65_72_69_6E_67 } ;
("lineargradient") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_61_72_67_72_61_64_69_65_6E_74 } ;
("aria-level") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_65_76_65_6C } ;
("otherwise") => { $ crate :: ATOM_LOCALNAME__6F_74_68_65_72_77_69_73_65 } ;
("word-spacing") => { $ crate :: ATOM_LOCALNAME__77_6F_72_64_2D_73_70_61_63_69_6E_67 } ;
("enable-background") => { $ crate :: ATOM_LOCALNAME__65_6E_61_62_6C_65_2D_62_61_63_6B_67_72_6F_75_6E_64 } ;
("fedisplacementmap") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_73_70_6C_61_63_65_6D_65_6E_74_6D_61_70 } ;
("onbefordeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_64_65_61_63_74_69_76_61_74_65 } ;
("aria-busy") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_62_75_73_79 } ;
("preservealpha") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_6C_70_68_61 } ;
("x-height") => { $ crate :: ATOM_LOCALNAME__78_2D_68_65_69_67_68_74 } ;
("animateColor") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_43_6F_6C_6F_72 } ;
("fediffuselighting") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_66_66_75_73_65_6C_69_67_68_74_69_6E_67 } ;
("seamless") => { $ crate :: ATOM_LOCALNAME__73_65_61_6D_6C_65_73_73 } ;
("onrepeat") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_70_65_61_74 } ;
("textPath") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_50_61_74_68 } ;
("aria-setsize") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_74_73_69_7A_65 } ;
("glyphRef") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_52_65_66 } ;
("ondatasetchanged") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_68_61_6E_67_65_64 } ;
("displaystyle") => { $ crate :: ATOM_LOCALNAME__64_69_73_70_6C_61_79_73_74_79_6C_65 } ;
("specularconstant") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_63_6F_6E_73_74_61_6E_74 } ;
("contentstyletype") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_73_74_79_6C_65_74_79_70_65 } ;
("formnovalidate") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_6E_6F_76_61_6C_69_64_61_74_65 } ;
("tablevalues") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65_76_61_6C_75_65_73 } ;
("maskcontentunits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_63_6F_6E_74_65_6E_74_75_6E_69_74_73 } ;
("onresize") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_73_69_7A_65 } ;
("inputmode") => { $ crate :: ATOM_LOCALNAME__69_6E_70_75_74_6D_6F_64_65 } ;
("onmousemove") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6D_6F_76_65 } ;
("keypoints") => { $ crate :: ATOM_LOCALNAME__6B_65_79_70_6F_69_6E_74_73 } ;
("equalcolumns") => { $ crate :: ATOM_LOCALNAME__65_71_75_61_6C_63_6F_6C_75_6D_6E_73 } ;
("selection") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_69_6F_6E } ;
("baseProfile") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_50_72_6F_66_69_6C_65 } ;
("altGlyphItem") => { $ crate :: ATOM_LOCALNAME__61_6C_74_47_6C_79_70_68_49_74_65_6D } ;
("clipPathUnits") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_50_61_74_68_55_6E_69_74_73 } ;
("viewtarget") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_74_61_72_67_65_74 } ;
("figcaption") => { $ crate :: ATOM_LOCALNAME__66_69_67_63_61_70_74_69_6F_6E } ;
("accent-height") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74_2D_68_65_69_67_68_74 } ;
("font-stretch") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_72_65_74_63_68 } ;
("font-face-uri") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_75_72_69 } ;
("equalrows") => { $ crate :: ATOM_LOCALNAME__65_71_75_61_6C_72_6F_77_73 } ;
("startOffset") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74_4F_66_66_73_65_74 } ;
("animateMotion") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_4D_6F_74_69_6F_6E } ;
("feMorphology") => { $ crate :: ATOM_LOCALNAME__66_65_4D_6F_72_70_68_6F_6C_6F_67_79 } ;
("controls") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_72_6F_6C_73 } ;
("contextmenu") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_78_74_6D_65_6E_75 } ;
("ondeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_65_61_63_74_69_76_61_74_65 } ;
("autofocus") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_66_6F_63_75_73 } ;
("flood-opacity") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_64_2D_6F_70_61_63_69_74_79 } ;
("attributeType") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_54_79_70_65 } ;
("numoctaves") => { $ crate :: ATOM_LOCALNAME__6E_75_6D_6F_63_74_61_76_65_73 } ;
("aria-multiline") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_6C_69_6E_65 } ;
("zoomAndPan") => { $ crate :: ATOM_LOCALNAME__7A_6F_6F_6D_41_6E_64_50_61_6E } ;
("accumulate") => { $ crate :: ATOM_LOCALNAME__61_63_63_75_6D_75_6C_61_74_65 } ;
("minlength") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_6C_65_6E_67_74_68 } ;
("quotient") => { $ crate :: ATOM_LOCALNAME__71_75_6F_74_69_65_6E_74 } ;
("attributename") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_6E_61_6D_65 } ;
("textlength") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_6C_65_6E_67_74_68 } ;
("radialGradient") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_61_6C_47_72_61_64_69_65_6E_74 } ;
("autoplay") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_70_6C_61_79 } ;
("onformchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_72_6D_63_68_61_6E_67_65 } ;
("superscriptshift") => { $ crate :: ATOM_LOCALNAME__73_75_70_65_72_73_63_72_69_70_74_73_68_69_66_74 } ;
("columnlines") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_6C_69_6E_65_73 } ;
("color-interpolation") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E } ;
("gradienttransform") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_74_72_61_6E_73_66_6F_72_6D } ;
("missing-glyph") => { $ crate :: ATOM_LOCALNAME__6D_69_73_73_69_6E_67_2D_67_6C_79_70_68 } ;
("onrowsinserted") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_73_69_6E_73_65_72_74_65_64 } ;
("kernelMatrix") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_4D_61_74_72_69_78 } ;
("onpageshow") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_67_65_73_68_6F_77 } ;
("visibility") => { $ crate :: ATOM_LOCALNAME__76_69_73_69_62_69_6C_69_74_79 } ;
("viewTarget") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_54_61_72_67_65_74 } ;
("feDisplacementMap") => { $ crate :: ATOM_LOCALNAME__66_65_44_69_73_70_6C_61_63_65_6D_65_6E_74_4D_61_70 } ;
("aria-valuemin") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_69_6E } ;
("kernelUnitLength") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_55_6E_69_74_4C_65_6E_67_74_68 } ;
("imaginary") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79 } ;
("cellpadding") => { $ crate :: ATOM_LOCALNAME__63_65_6C_6C_70_61_64_64_69_6E_67 } ;
("formmethod") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_6D_65_74_68_6F_64 } ;
("overline-thickness") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 } ;
("onmouseup") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_75_70 } ;
("onbounce") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_6F_75_6E_63_65 } ;
("unselectable") => { $ crate :: ATOM_LOCALNAME__75_6E_73_65_6C_65_63_74_61_62_6C_65 } ;
("aria-dropeffect") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_72_6F_70_65_66_66_65_63_74 } ;
("codebase") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65_62_61_73_65 } ;
("malignmark") => { $ crate :: ATOM_LOCALNAME__6D_61_6C_69_67_6E_6D_61_72_6B } ;
("pathLength") => { $ crate :: ATOM_LOCALNAME__70_61_74_68_4C_65_6E_67_74_68 } ;
("interval") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_76_61_6C } ;
("onmessage") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_65_73_73_61_67_65 } ;
("laplacian") => { $ crate :: ATOM_LOCALNAME__6C_61_70_6C_61_63_69_61_6E } ;
("xml:base") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_62_61_73_65 } ;
("aria-checked") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_68_65_63_6B_65_64 } ;
("exponent") => { $ crate :: ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74 } ;
("onmouseout") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6F_75_74 } ;
("veryverythinmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("onbeforepaste") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_70_61_73_74_65 } ;
("lengthadjust") => { $ crate :: ATOM_LOCALNAME__6C_65_6E_67_74_68_61_64_6A_75_73_74 } ;
("patterntransform") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_74_72_61_6E_73_66_6F_72_6D } ;
("determinant") => { $ crate :: ATOM_LOCALNAME__64_65_74_65_72_6D_69_6E_61_6E_74 } ;
("progress") => { $ crate :: ATOM_LOCALNAME__70_72_6F_67_72_65_73_73 } ;
("unicode-bidi") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_62_69_64_69 } ;
("vert-adv-y") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_61_64_76_2D_79 } ;
("strikethrough-thickness") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_74_68_69_63_6B_6E_65_73_73 } ;
("onmouseenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_65_6E_74_65_72 } ;
("multiple") => { $ crate :: ATOM_LOCALNAME__6D_75_6C_74_69_70_6C_65 } ;
("symmetric") => { $ crate :: ATOM_LOCALNAME__73_79_6D_6D_65_74_72_69_63 } ;
("fegaussianblur") => { $ crate :: ATOM_LOCALNAME__66_65_67_61_75_73_73_69_61_6E_62_6C_75_72 } ;
("altglyph") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68 } ;
("ondragenter") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_74_65_72 } ;
("dataformatas") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_66_6F_72_6D_61_74_61_73 } ;
("xlink:href") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_68_72_65_66 } ;
("integrity") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_67_72_69_74_79 } ;
("itemprop") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_70_72_6F_70 } ;
("aria-controls") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_6F_6E_74_72_6F_6C_73 } ;
("font-size-adjust") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_69_7A_65_2D_61_64_6A_75_73_74 } ;
("http-equiv") => { $ crate :: ATOM_LOCALNAME__68_74_74_70_2D_65_71_75_69_76 } ;
("onbeforeactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_61_63_74_69_76_61_74_65 } ;
("startoffset") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74_6F_66_66_73_65_74 } ;
("onafterupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_66_74_65_72_75_70_64_61_74_65 } ;
("basefont") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_66_6F_6E_74 } ;
("onlanguagechange") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_61_6E_67_75_61_67_65_63_68_61_6E_67_65 } ;
("rationals") => { $ crate :: ATOM_LOCALNAME__72_61_74_69_6F_6E_61_6C_73 } ;
("ondatasetcomplete") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_61_74_61_73_65_74_63_6F_6D_70_6C_65_74_65 } ;
("oncellchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_65_6C_6C_63_68_61_6E_67_65 } ;
("onactivate") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_63_74_69_76_61_74_65 } ;
("onmovestart") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65_73_74_61_72_74 } ;
("line-height") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_2D_68_65_69_67_68_74 } ;
("menclose") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_63_6C_6F_73_65 } ;
("onerrorupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_72_72_6F_72_75_70_64_61_74_65 } ;
("framespacing") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_73_70_61_63_69_6E_67 } ;
("naturalnumbers") => { $ crate :: ATOM_LOCALNAME__6E_61_74_75_72_61_6C_6E_75_6D_62_65_72_73 } ;
("notation") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_61_74_69_6F_6E } ;
("lowlimit") => { $ crate :: ATOM_LOCALNAME__6C_6F_77_6C_69_6D_69_74 } ;
("listener") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74_65_6E_65_72 } ;
("vert-origin-x") => { $ crate :: ATOM_LOCALNAME__76_65_72_74_2D_6F_72_69_67_69_6E_2D_78 } ;
("repeatcount") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_63_6F_75_6E_74 } ;
("aria-templateid") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_74_65_6D_70_6C_61_74_65_69_64 } ;
("baseline") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65 } ;
("template") => { $ crate :: ATOM_LOCALNAME__74_65_6D_70_6C_61_74_65 } ;
("specularexponent") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_65_78_70_6F_6E_65_6E_74 } ;
("writing-mode") => { $ crate :: ATOM_LOCALNAME__77_72_69_74_69_6E_67_2D_6D_6F_64_65 } ;
("onfocusin") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73_69_6E } ;
("patternContentUnits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_43_6F_6E_74_65_6E_74_55_6E_69_74_73 } ;
("feconvolvematrix") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6E_76_6F_6C_76_65_6D_61_74_72_69_78 } ;
("mathematical") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_65_6D_61_74_69_63_61_6C } ;
("font-face-format") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_66_6F_72_6D_61_74 } ;
("onrowsdelete") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_6F_77_73_64_65_6C_65_74_65 } ;
("additive") => { $ crate :: ATOM_LOCALNAME__61_64_64_69_74_69_76_65 } ;
("novalidate") => { $ crate :: ATOM_LOCALNAME__6E_6F_76_61_6C_69_64_61_74_65 } ;
("font-face") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65 } ;
("overline-position") => { $ crate :: ATOM_LOCALNAME__6F_76_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E } ;
("filterres") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_72_65_73 } ;
("matrixrow") => { $ crate :: ATOM_LOCALNAME__6D_61_74_72_69_78_72_6F_77 } ;
("mathvariant") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_76_61_72_69_61_6E_74 } ;
("onstorage") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_6F_72_61_67_65 } ;
("font-face-src") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_66_61_63_65_2D_73_72_63 } ;
("underline-thickness") => { $ crate :: ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_74_68_69_63_6B_6E_65_73_73 } ;
("feturbulence") => { $ crate :: ATOM_LOCALNAME__66_65_74_75_72_62_75_6C_65_6E_63_65 } ;
("optgroup") => { $ crate :: ATOM_LOCALNAME__6F_70_74_67_72_6F_75_70 } ;
("onscroll") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_63_72_6F_6C_6C } ;
("frameborder") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_62_6F_72_64_65_72 } ;
("conjugate") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_6A_75_67_61_74_65 } ;
("gradientTransform") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_54_72_61_6E_73_66_6F_72_6D } ;
("specularConstant") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_43_6F_6E_73_74_61_6E_74 } ;
("mathsize") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_73_69_7A_65 } ;
("markerUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_55_6E_69_74_73 } ;
("complexes") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_6C_65_78_65_73 } ;
("pathlength") => { $ crate :: ATOM_LOCALNAME__70_61_74_68_6C_65_6E_67_74_68 } ;
("linebreak") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_62_72_65_61_6B } ;
("glyph-name") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6E_61_6D_65 } ;
("unicode-range") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65_2D_72_61_6E_67_65 } ;
("baseprofile") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_70_72_6F_66_69_6C_65 } ;
("required") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64 } ;
("readonly") => { $ crate :: ATOM_LOCALNAME__72_65_61_64_6F_6E_6C_79 } ;
("onmouseleave") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_6C_65_61_76_65 } ;
("integers") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_67_65_72_73 } ;
("marker-end") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_65_6E_64 } ;
("rowspacing") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73_70_61_63_69_6E_67 } ;
("diffuseConstant") => { $ crate :: ATOM_LOCALNAME__64_69_66_66_75_73_65_43_6F_6E_73_74_61_6E_74 } ;
("autocomplete") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_63_6F_6D_70_6C_65_74_65 } ;
("repeatdur") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_64_75_72 } ;
("definitionURL") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_55_52_4C } ;
("mathbackground") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_62_61_63_6B_67_72_6F_75_6E_64 } ;
("feGaussianBlur") => { $ crate :: ATOM_LOCALNAME__66_65_47_61_75_73_73_69_61_6E_42_6C_75_72 } ;
("amplitude") => { $ crate :: ATOM_LOCALNAME__61_6D_70_6C_69_74_75_64_65 } ;
("onmoveend") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65_65_6E_64 } ;
("download") => { $ crate :: ATOM_LOCALNAME__64_6F_77_6E_6C_6F_61_64 } ;
("diffuseconstant") => { $ crate :: ATOM_LOCALNAME__64_69_66_66_75_73_65_63_6F_6E_73_74_61_6E_74 } ;
("externalresourcesrequired") => { $ crate :: ATOM_LOCALNAME__65_78_74_65_72_6E_61_6C_72_65_73_6F_75_72_63_65_73_72_65_71_75_69_72_65_64 } ;
("aria-autocomplete") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_75_74_6F_63_6F_6D_70_6C_65_74_65 } ;
("keytimes") => { $ crate :: ATOM_LOCALNAME__6B_65_79_74_69_6D_65_73 } ;
("altglyphdef") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_64_65_66 } ;
("codetype") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65_74_79_70_65 } ;
("background") => { $ crate :: ATOM_LOCALNAME__62_61_63_6B_67_72_6F_75_6E_64 } ;
("fieldset") => { $ crate :: ATOM_LOCALNAME__66_69_65_6C_64_73_65_74 } ;
("partialdiff") => { $ crate :: ATOM_LOCALNAME__70_61_72_74_69_61_6C_64_69_66_66 } ;
("pointsatx") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_78 } ;
("formaction") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_61_63_74_69_6F_6E } ;
("xlink:show") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_73_68_6F_77 } ;
("spreadmethod") => { $ crate :: ATOM_LOCALNAME__73_70_72_65_61_64_6D_65_74_68_6F_64 } ;
("filterUnits") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_55_6E_69_74_73 } ;
("nomodule") => { $ crate :: ATOM_LOCALNAME__6E_6F_6D_6F_64_75_6C_65 } ;
("fontstyle") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_73_74_79_6C_65 } ;
("ondragend") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_65_6E_64 } ;
("fecolormatrix") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6C_6F_72_6D_61_74_72_69_78 } ;
("itemtype") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_74_79_70_65 } ;
("attributetype") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_74_79_70_65 } ;
("mprescripts") => { $ crate :: ATOM_LOCALNAME__6D_70_72_65_73_63_72_69_70_74_73 } ;
("orientation") => { $ crate :: ATOM_LOCALNAME__6F_72_69_65_6E_74_61_74_69_6F_6E } ;
("keyPoints") => { $ crate :: ATOM_LOCALNAME__6B_65_79_50_6F_69_6E_74_73 } ;
("maskUnits") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B_55_6E_69_74_73 } ;
("pointer-events") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_65_72_2D_65_76_65_6E_74_73 } ;
("valuetype") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65_74_79_70_65 } ;
("marginwidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_67_69_6E_77_69_64_74_68 } ;
("statechange") => { $ crate :: ATOM_LOCALNAME__73_74_61_74_65_63_68_61_6E_67_65 } ;
("onfilterchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_69_6C_74_65_72_63_68_61_6E_67_65 } ;
("rowlines") => { $ crate :: ATOM_LOCALNAME__72_6F_77_6C_69_6E_65_73 } ;
("aria-describedby") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_65_73_63_72_69_62_65_64_62_79 } ;
("lighting-color") => { $ crate :: ATOM_LOCALNAME__6C_69_67_68_74_69_6E_67_2D_63_6F_6C_6F_72 } ;
("kernelunitlength") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_65_6C_75_6E_69_74_6C_65_6E_67_74_68 } ;
("equivalent") => { $ crate :: ATOM_LOCALNAME__65_71_75_69_76_61_6C_65_6E_74 } ;
("aria-relevant") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_72_65_6C_65_76_61_6E_74 } ;
("stroke-linecap") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6C_69_6E_65_63_61_70 } ;
("disabled") => { $ crate :: ATOM_LOCALNAME__64_69_73_61_62_6C_65_64 } ;
("patternunits") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_75_6E_69_74_73 } ;
("strikethrough-position") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65_74_68_72_6F_75_67_68_2D_70_6F_73_69_74_69_6F_6E } ;
("ychannelselector") => { $ crate :: ATOM_LOCALNAME__79_63_68_61_6E_6E_65_6C_73_65_6C_65_63_74_6F_72 } ;
("") => { $ crate :: ATOM_LOCALNAME_ } ;
("requiredfeatures") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_66_65_61_74_75_72_65_73 } ;
("fePointLight") => { $ crate :: ATOM_LOCALNAME__66_65_50_6F_69_6E_74_4C_69_67_68_74 } ;
("onforminput") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_72_6D_69_6E_70_75_74 } ;
("horiz-origin-y") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_79 } ;
("keyTimes") => { $ crate :: ATOM_LOCALNAME__6B_65_79_54_69_6D_65_73 } ;
("requiredextensions") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_65_78_74_65_6E_73_69_6F_6E_73 } ;
("verythickmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("primitiveUnits") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_55_6E_69_74_73 } ;
("pointsaty") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_61_74_79 } ;
("scrolldelay") => { $ crate :: ATOM_LOCALNAME__73_63_72_6F_6C_6C_64_65_6C_61_79 } ;
("colgroup") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_67_72_6F_75_70 } ;
("aria-expanded") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_65_78_70_61_6E_64_65_64 } ;
("font-weight") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_77_65_69_67_68_74 } ;
("baseFrequency") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_46_72_65_71_75_65_6E_63_79 } ;
("operator") => { $ crate :: ATOM_LOCALNAME__6F_70_65_72_61_74_6F_72 } ;
("animateTransform") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65_54_72_61_6E_73_66_6F_72_6D } ;
("transform") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_66_6F_72_6D } ;
("marker-mid") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_2D_6D_69_64 } ;
("mlabeledtr") => { $ crate :: ATOM_LOCALNAME__6D_6C_61_62_65_6C_65_64_74_72 } ;
("altglyphitem") => { $ crate :: ATOM_LOCALNAME__61_6C_74_67_6C_79_70_68_69_74_65_6D } ;
("markerHeight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_48_65_69_67_68_74 } ;
("fill-rule") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C_2D_72_75_6C_65 } ;
("ideographic") => { $ crate :: ATOM_LOCALNAME__69_64_65_6F_67_72_61_70_68_69_63 } ;
("yChannelSelector") => { $ crate :: ATOM_LOCALNAME__79_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 } ;
("datetime") => { $ crate :: ATOM_LOCALNAME__64_61_74_65_74_69_6D_65 } ;
("linethickness") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_74_68_69_63_6B_6E_65_73_73 } ;
("repeat-min") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_2D_6D_69_6E } ;
("markerWidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_57_69_64_74_68 } ;
("stretchy") => { $ crate :: ATOM_LOCALNAME__73_74_72_65_74_63_68_79 } ;
("glyph-orientation-horizontal") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_68_6F_72_69_7A_6F_6E_74_61_6C } ;
("exponentiale") => { $ crate :: ATOM_LOCALNAME__65_78_70_6F_6E_65_6E_74_69_61_6C_65 } ;
("edgemode") => { $ crate :: ATOM_LOCALNAME__65_64_67_65_6D_6F_64_65 } ;
("stroke-opacity") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6F_70_61_63_69_74_79 } ;
("femergenode") => { $ crate :: ATOM_LOCALNAME__66_65_6D_65_72_67_65_6E_6F_64_65 } ;
("aria-haspopup") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_68_61_73_70_6F_70_75_70 } ;
("variance") => { $ crate :: ATOM_LOCALNAME__76_61_72_69_61_6E_63_65 } ;
("color-interpolation-filters") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72_2D_69_6E_74_65_72_70_6F_6C_61_74_69_6F_6E_2D_66_69_6C_74_65_72_73 } ;
("textarea") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_61_72_65_61 } ;
("radiogroup") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_6F_67_72_6F_75_70 } ;
("allowfullscreen") => { $ crate :: ATOM_LOCALNAME__61_6C_6C_6F_77_66_75_6C_6C_73_63_72_65_65_6E } ;
("domainofapplication") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_61_69_6E_6F_66_61_70_70_6C_69_63_61_74_69_6F_6E } ;
("feOffset") => { $ crate :: ATOM_LOCALNAME__66_65_4F_66_66_73_65_74 } ;
("emptyset") => { $ crate :: ATOM_LOCALNAME__65_6D_70_74_79_73_65_74 } ;
("occurrence") => { $ crate :: ATOM_LOCALNAME__6F_63_63_75_72_72_65_6E_63_65 } ;
("image-rendering") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_65_2D_72_65_6E_64_65_72_69_6E_67 } ;
("calcMode") => { $ crate :: ATOM_LOCALNAME__63_61_6C_63_4D_6F_64_65 } ;
("patternTransform") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E_54_72_61_6E_73_66_6F_72_6D } ;
("onpagehide") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_67_65_68_69_64_65 } ;
("feComponentTransfer") => { $ crate :: ATOM_LOCALNAME__66_65_43_6F_6D_70_6F_6E_65_6E_74_54_72_61_6E_73_66_65_72 } ;
("feDropShadow") => { $ crate :: ATOM_LOCALNAME__66_65_44_72_6F_70_53_68_61_64_6F_77 } ;
("intersect") => { $ crate :: ATOM_LOCALNAME__69_6E_74_65_72_73_65_63_74 } ;
("horiz-origin-x") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_6F_72_69_67_69_6E_2D_78 } ;
("definitionurl") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_75_72_6C } ;
("xlink:role") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_72_6F_6C_65 } ;
("accentunder") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74_75_6E_64_65_72 } ;
("stop-color") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70_2D_63_6F_6C_6F_72 } ;
("feMergeNode") => { $ crate :: ATOM_LOCALNAME__66_65_4D_65_72_67_65_4E_6F_64_65 } ;
("xlink:title") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_69_74_6C_65 } ;
("fontweight") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_77_65_69_67_68_74 } ;
("aria-flowto") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_66_6C_6F_77_74_6F } ;
("fedistantlight") => { $ crate :: ATOM_LOCALNAME__66_65_64_69_73_74_61_6E_74_6C_69_67_68_74 } ;
("onbeforeupdate") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_70_64_61_74_65 } ;
("v-alphabetic") => { $ crate :: ATOM_LOCALNAME__76_2D_61_6C_70_68_61_62_65_74_69_63 } ;
("onchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_68_61_6E_67_65 } ;
("stroke-miterlimit") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65_2D_6D_69_74_65_72_6C_69_6D_69_74 } ;
("divergence") => { $ crate :: ATOM_LOCALNAME__64_69_76_65_72_67_65_6E_63_65 } ;
("linearGradient") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65_61_72_47_72_61_64_69_65_6E_74 } ;
("limitingConeAngle") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_43_6F_6E_65_41_6E_67_6C_65 } ;
("clip-path") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_2D_70_61_74_68 } ;
("scriptlevel") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_6C_65_76_65_6C } ;
("onunload") => { $ crate :: ATOM_LOCALNAME__6F_6E_75_6E_6C_6F_61_64 } ;
("munderover") => { $ crate :: ATOM_LOCALNAME__6D_75_6E_64_65_72_6F_76_65_72 } ;
("repeatCount") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74_43_6F_75_6E_74 } ;
("placeholder") => { $ crate :: ATOM_LOCALNAME__70_6C_61_63_65_68_6F_6C_64_65_72 } ;
("vectorproduct") => { $ crate :: ATOM_LOCALNAME__76_65_63_74_6F_72_70_72_6F_64_75_63_74 } ;
("columnspan") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_73_70_61_6E } ;
("underline-position") => { $ crate :: ATOM_LOCALNAME__75_6E_64_65_72_6C_69_6E_65_2D_70_6F_73_69_74_69_6F_6E } ;
("baseline-shift") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_6C_69_6E_65_2D_73_68_69_66_74 } ;
("aria-secret") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_73_65_63_72_65_74 } ;
("referrerpolicy") => { $ crate :: ATOM_LOCALNAME__72_65_66_65_72_72_65_72_70_6F_6C_69_63_79 } ;
("calcmode") => { $ crate :: ATOM_LOCALNAME__63_61_6C_63_6D_6F_64_65 } ;
("onhashchange") => { $ crate :: ATOM_LOCALNAME__6F_6E_68_61_73_68_63_68_61_6E_67_65 } ;
("columnalign") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_61_6C_69_67_6E } ;
("marginheight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_67_69_6E_68_65_69_67_68_74 } ;
("aria-grab") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_67_72_61_62 } ;
("specification") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_69_66_69_63_61_74_69_6F_6E } ;
("formenctype") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_65_6E_63_74_79_70_65 } ;
("limitingconeangle") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74_69_6E_67_63_6F_6E_65_61_6E_67_6C_65 } ;
("cartesianproduct") => { $ crate :: ATOM_LOCALNAME__63_61_72_74_65_73_69_61_6E_70_72_6F_64_75_63_74 } ;
("annotation") => { $ crate :: ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E } ;
("factorof") => { $ crate :: ATOM_LOCALNAME__66_61_63_74_6F_72_6F_66 } ;
("momentabout") => { $ crate :: ATOM_LOCALNAME__6D_6F_6D_65_6E_74_61_62_6F_75_74 } ;
("aria-valuemax") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6D_61_78 } ;
("lengthAdjust") => { $ crate :: ATOM_LOCALNAME__6C_65_6E_67_74_68_41_64_6A_75_73_74 } ;
("mediummathspace") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_75_6D_6D_61_74_68_73_70_61_63_65 } ;
("panose-1") => { $ crate :: ATOM_LOCALNAME__70_61_6E_6F_73_65_2D_31 } ;
("movablelimits") => { $ crate :: ATOM_LOCALNAME__6D_6F_76_61_62_6C_65_6C_69_6D_69_74_73 } ;
("filterunits") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72_75_6E_69_74_73 } ;
("stitchTiles") => { $ crate :: ATOM_LOCALNAME__73_74_69_74_63_68_54_69_6C_65_73 } ;
("primitiveunits") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_69_74_69_76_65_75_6E_69_74_73 } ;
("factorial") => { $ crate :: ATOM_LOCALNAME__66_61_63_74_6F_72_69_61_6C } ;
("fecomponenttransfer") => { $ crate :: ATOM_LOCALNAME__66_65_63_6F_6D_70_6F_6E_65_6E_74_74_72_61_6E_73_66_65_72 } ;
("systemLanguage") => { $ crate :: ATOM_LOCALNAME__73_79_73_74_65_6D_4C_61_6E_67_75_61_67_65 } ;
("onbeforeunload") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_75_6E_6C_6F_61_64 } ;
("noresize") => { $ crate :: ATOM_LOCALNAME__6E_6F_72_65_73_69_7A_65 } ;
("dominant-baseline") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_69_6E_61_6E_74_2D_62_61_73_65_6C_69_6E_65 } ;
("animation") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_69_6F_6E } ;
("preserveAspectRatio") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_73_70_65_63_74_52_61_74_69_6F } ;
("clippathunits") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_70_61_74_68_75_6E_69_74_73 } ;
("gradientUnits") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_55_6E_69_74_73 } ;
("imaginaryi") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_69_6E_61_72_79_69 } ;
("oncontextmenu") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_6E_74_65_78_74_6D_65_6E_75 } ;
("noframes") => { $ crate :: ATOM_LOCALNAME__6E_6F_66_72_61_6D_65_73 } ;
("rowalign") => { $ crate :: ATOM_LOCALNAME__72_6F_77_61_6C_69_67_6E } ;
("horiz-adv-x") => { $ crate :: ATOM_LOCALNAME__68_6F_72_69_7A_2D_61_64_76_2D_78 } ;
("xlink:arcrole") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_72_63_72_6F_6C_65 } ;
("pointsAtZ") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73_41_74_5A } ;
("surfaceScale") => { $ crate :: ATOM_LOCALNAME__73_75_72_66_61_63_65_53_63_61_6C_65 } ;
("glyph-orientation-vertical") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68_2D_6F_72_69_65_6E_74_61_74_69_6F_6E_2D_76_65_72_74_69_63_61_6C } ;
("markerwidth") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_77_69_64_74_68 } ;
("manifest") => { $ crate :: ATOM_LOCALNAME__6D_61_6E_69_66_65_73_74 } ;
("xlink:type") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_74_79_70_65 } ;
("text-anchor") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_61_6E_63_68_6F_72 } ;
("decoding") => { $ crate :: ATOM_LOCALNAME__64_65_63_6F_64_69_6E_67 } ;
("xmlns:xlink") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_6E_73_3A_78_6C_69_6E_6B } ;
("noscript") => { $ crate :: ATOM_LOCALNAME__6E_6F_73_63_72_69_70_74 } ;
("onselect") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74 } ;
("xml:space") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_3A_73_70_61_63_65 } ;
("aria-invalid") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_69_6E_76_61_6C_69_64 } ;
("transpose") => { $ crate :: ATOM_LOCALNAME__74_72_61_6E_73_70_6F_73_65 } ;
("selector") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74_6F_72 } ;
("property") => { $ crate :: ATOM_LOCALNAME__70_72_6F_70_65_72_74_79 } ;
("aria-pressed") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_70_72_65_73_73_65_64 } ;
("definition-src") => { $ crate :: ATOM_LOCALNAME__64_65_66_69_6E_69_74_69_6F_6E_2D_73_72_63 } ;
("tableValues") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65_56_61_6C_75_65_73 } ;
("aria-activedescendant") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_61_63_74_69_76_65_64_65_73_63_65_6E_64_61_6E_74 } ;
("text-rendering") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_2D_72_65_6E_64_65_72_69_6E_67 } ;
("semantics") => { $ crate :: ATOM_LOCALNAME__73_65_6D_61_6E_74_69_63_73 } ;
("separators") => { $ crate :: ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72_73 } ;
("scriptminsize") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74_6D_69_6E_73_69_7A_65 } ;
("fill-opacity") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C_2D_6F_70_61_63_69_74_79 } ;
("font-variant") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_76_61_72_69_61_6E_74 } ;
("aria-labelledby") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6C_61_62_65_6C_6C_65_64_62_79 } ;
("columnwidth") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_75_6D_6E_77_69_64_74_68 } ;
("alignment-baseline") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_2D_62_61_73_65_6C_69_6E_65 } ;
("fepointlight") => { $ crate :: ATOM_LOCALNAME__66_65_70_6F_69_6E_74_6C_69_67_68_74 } ;
("xChannelSelector") => { $ crate :: ATOM_LOCALNAME__78_43_68_61_6E_6E_65_6C_53_65_6C_65_63_74_6F_72 } ;
("foreignObject") => { $ crate :: ATOM_LOCALNAME__66_6F_72_65_69_67_6E_4F_62_6A_65_63_74 } ;
("bevelled") => { $ crate :: ATOM_LOCALNAME__62_65_76_65_6C_6C_65_64 } ;
("feTurbulence") => { $ crate :: ATOM_LOCALNAME__66_65_54_75_72_62_75_6C_65_6E_63_65 } ;
("fespotlight") => { $ crate :: ATOM_LOCALNAME__66_65_73_70_6F_74_6C_69_67_68_74 } ;
("prsubset") => { $ crate :: ATOM_LOCALNAME__70_72_73_75_62_73_65_74 } ;
("basefrequency") => { $ crate :: ATOM_LOCALNAME__62_61_73_65_66_72_65_71_75_65_6E_63_79 } ;
("gradientunits") => { $ crate :: ATOM_LOCALNAME__67_72_61_64_69_65_6E_74_75_6E_69_74_73 } ;
("autosubmit") => { $ crate :: ATOM_LOCALNAME__61_75_74_6F_73_75_62_6D_69_74 } ;
("eulergamma") => { $ crate :: ATOM_LOCALNAME__65_75_6C_65_72_67_61_6D_6D_61 } ;
("oninvalid") => { $ crate :: ATOM_LOCALNAME__6F_6E_69_6E_76_61_6C_69_64 } ;
("draggable") => { $ crate :: ATOM_LOCALNAME__64_72_61_67_67_61_62_6C_65 } ;
("polyline") => { $ crate :: ATOM_LOCALNAME__70_6F_6C_79_6C_69_6E_65 } ;
("maligngroup") => { $ crate :: ATOM_LOCALNAME__6D_61_6C_69_67_6E_67_72_6F_75_70 } ;
("textLength") => { $ crate :: ATOM_LOCALNAME__74_65_78_74_4C_65_6E_67_74_68 } ;
("onsubmit") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_75_62_6D_69_74 } ;
("mmultiscripts") => { $ crate :: ATOM_LOCALNAME__6D_6D_75_6C_74_69_73_63_72_69_70_74_73 } ;
("contentScriptType") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74_53_63_72_69_70_74_54_79_70_65 } ;
("codomain") => { $ crate :: ATOM_LOCALNAME__63_6F_64_6F_6D_61_69_6E } ;
("oncontrolselect") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_6E_74_72_6F_6C_73_65_6C_65_63_74 } ;
("ondblclick") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_62_6C_63_6C_69_63_6B } ;
("accesskey") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_73_73_6B_65_79 } ;
("menuitem") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_75_69_74_65_6D } ;
("aria-channel") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_63_68_61_6E_6E_65_6C } ;
("formtarget") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_74_61_72_67_65_74 } ;
("onfinish") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_69_6E_69_73_68 } ;
("clippath") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_70_61_74_68 } ;
("ondragleave") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_6C_65_61_76_65 } ;
("ondragdrop") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_64_72_6F_70 } ;
("actiontype") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_6F_6E_74_79_70_65 } ;
("solidcolor") => { $ crate :: ATOM_LOCALNAME__73_6F_6C_69_64_63_6F_6C_6F_72 } ;
("font-style") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_2D_73_74_79_6C_65 } ;
("outerproduct") => { $ crate :: ATOM_LOCALNAME__6F_75_74_65_72_70_72_6F_64_75_63_74 } ;
("notanumber") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_61_6E_75_6D_62_65_72 } ;
("requiredFeatures") => { $ crate :: ATOM_LOCALNAME__72_65_71_75_69_72_65_64_46_65_61_74_75_72_65_73 } ;
("onselectstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_65_6C_65_63_74_73_74_61_72_74 } ;
("ondragstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67_73_74_61_72_74 } ;
("alignmentscope") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E_6D_65_6E_74_73_63_6F_70_65 } ;
("scrolling") => { $ crate :: ATOM_LOCALNAME__73_63_72_6F_6C_6C_69_6E_67 } ;
("onpropertychange") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_72_6F_70_65_72_74_79_63_68_61_6E_67_65 } ;
("onkeypress") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_70_72_65_73_73 } ;
("annotation-xml") => { $ crate :: ATOM_LOCALNAME__61_6E_6E_6F_74_61_74_69_6F_6E_2D_78_6D_6C } ;
("stdDeviation") => { $ crate :: ATOM_LOCALNAME__73_74_64_44_65_76_69_61_74_69_6F_6E } ;
("specularExponent") => { $ crate :: ATOM_LOCALNAME__73_70_65_63_75_6C_61_72_45_78_70_6F_6E_65_6E_74 } ;
("veryverythickmathspace") => { $ crate :: ATOM_LOCALNAME__76_65_72_79_76_65_72_79_74_68_69_63_6B_6D_61_74_68_73_70_61_63_65 } ;
("frameset") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65_73_65_74 } ;
("onkeydown") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_64_6F_77_6E } ;
("aria-multiselectable") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_6D_75_6C_74_69_73_65_6C_65_63_74_61_62_6C_65 } ;
("fontsize") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74_73_69_7A_65 } ;
("onmousewheel") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_75_73_65_77_68_65_65_6C } ;
("onbeforecut") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_66_6F_72_65_63_75_74 } ;
("preserveAlpha") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_41_6C_70_68_61 } ;
("mathcolor") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68_63_6F_6C_6F_72 } ;
("attributeName") => { $ crate :: ATOM_LOCALNAME__61_74_74_72_69_62_75_74_65_4E_61_6D_65 } ;
("aria-disabled") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_64_69_73_61_62_6C_65_64 } ;
("preserveaspectratio") => { $ crate :: ATOM_LOCALNAME__70_72_65_73_65_72_76_65_61_73_70_65_63_74_72_61_74_69_6F } ;
("v-ideographic") => { $ crate :: ATOM_LOCALNAME__76_2D_69_64_65_6F_67_72_61_70_68_69_63 } ;
("thinmathspace") => { $ crate :: ATOM_LOCALNAME__74_68_69_6E_6D_61_74_68_73_70_61_63_65 } ;
("onlosecapture") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_6F_73_65_63_61_70_74_75_72_65 } ;
("aria-posinset") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_70_6F_73_69_6E_73_65_74 } ;
("separator") => { $ crate :: ATOM_LOCALNAME__73_65_70_61_72_61_74_6F_72 } ;
("multicol") => { $ crate :: ATOM_LOCALNAME__6D_75_6C_74_69_63_6F_6C } ;
("xlink:actuate") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B_3A_61_63_74_75_61_74_65 } ;
("markerheight") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72_68_65_69_67_68_74 } ;
("clipPath") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70_50_61_74_68 } ;
("blockquote") => { $ crate :: ATOM_LOCALNAME__62_6C_6F_63_6B_71_75_6F_74_65 } ;
("aria-valuenow") => { $ crate :: ATOM_LOCALNAME__61_72_69_61_2D_76_61_6C_75_65_6E_6F_77 } ;
("*") => { $ crate :: ATOM_LOCALNAME__2A } ;
("a") => { $ crate :: ATOM_LOCALNAME__61 } ;
("abbr") => { $ crate :: ATOM_LOCALNAME__61_62_62_72 } ;
("abs") => { $ crate :: ATOM_LOCALNAME__61_62_73 } ;
("accent") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_6E_74 } ;
("accept") => { $ crate :: ATOM_LOCALNAME__61_63_63_65_70_74 } ;
("acronym") => { $ crate :: ATOM_LOCALNAME__61_63_72_6F_6E_79_6D } ;
("action") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_6F_6E } ;
("active") => { $ crate :: ATOM_LOCALNAME__61_63_74_69_76_65 } ;
("actuate") => { $ crate :: ATOM_LOCALNAME__61_63_74_75_61_74_65 } ;
("address") => { $ crate :: ATOM_LOCALNAME__61_64_64_72_65_73_73 } ;
("align") => { $ crate :: ATOM_LOCALNAME__61_6C_69_67_6E } ;
("alink") => { $ crate :: ATOM_LOCALNAME__61_6C_69_6E_6B } ;
("alt") => { $ crate :: ATOM_LOCALNAME__61_6C_74 } ;
("altimg") => { $ crate :: ATOM_LOCALNAME__61_6C_74_69_6D_67 } ;
("alttext") => { $ crate :: ATOM_LOCALNAME__61_6C_74_74_65_78_74 } ;
("and") => { $ crate :: ATOM_LOCALNAME__61_6E_64 } ;
("animate") => { $ crate :: ATOM_LOCALNAME__61_6E_69_6D_61_74_65 } ;
("applet") => { $ crate :: ATOM_LOCALNAME__61_70_70_6C_65_74 } ;
("apply") => { $ crate :: ATOM_LOCALNAME__61_70_70_6C_79 } ;
("approx") => { $ crate :: ATOM_LOCALNAME__61_70_70_72_6F_78 } ;
("arccos") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_73 } ;
("arccosh") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_73_68 } ;
("arccot") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_74 } ;
("arccoth") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_6F_74_68 } ;
("arccsc") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_73_63 } ;
("arccsch") => { $ crate :: ATOM_LOCALNAME__61_72_63_63_73_63_68 } ;
("archive") => { $ crate :: ATOM_LOCALNAME__61_72_63_68_69_76_65 } ;
("arcrole") => { $ crate :: ATOM_LOCALNAME__61_72_63_72_6F_6C_65 } ;
("arcsec") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_65_63 } ;
("arcsech") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_65_63_68 } ;
("arcsin") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_69_6E } ;
("arcsinh") => { $ crate :: ATOM_LOCALNAME__61_72_63_73_69_6E_68 } ;
("arctan") => { $ crate :: ATOM_LOCALNAME__61_72_63_74_61_6E } ;
("arctanh") => { $ crate :: ATOM_LOCALNAME__61_72_63_74_61_6E_68 } ;
("area") => { $ crate :: ATOM_LOCALNAME__61_72_65_61 } ;
("arg") => { $ crate :: ATOM_LOCALNAME__61_72_67 } ;
("article") => { $ crate :: ATOM_LOCALNAME__61_72_74_69_63_6C_65 } ;
("ascent") => { $ crate :: ATOM_LOCALNAME__61_73_63_65_6E_74 } ;
("aside") => { $ crate :: ATOM_LOCALNAME__61_73_69_64_65 } ;
("async") => { $ crate :: ATOM_LOCALNAME__61_73_79_6E_63 } ;
("audio") => { $ crate :: ATOM_LOCALNAME__61_75_64_69_6F } ;
("axis") => { $ crate :: ATOM_LOCALNAME__61_78_69_73 } ;
("azimuth") => { $ crate :: ATOM_LOCALNAME__61_7A_69_6D_75_74_68 } ;
("b") => { $ crate :: ATOM_LOCALNAME__62 } ;
("base") => { $ crate :: ATOM_LOCALNAME__62_61_73_65 } ;
("bbox") => { $ crate :: ATOM_LOCALNAME__62_62_6F_78 } ;
("bdi") => { $ crate :: ATOM_LOCALNAME__62_64_69 } ;
("bdo") => { $ crate :: ATOM_LOCALNAME__62_64_6F } ;
("begin") => { $ crate :: ATOM_LOCALNAME__62_65_67_69_6E } ;
("bgcolor") => { $ crate :: ATOM_LOCALNAME__62_67_63_6F_6C_6F_72 } ;
("bgsound") => { $ crate :: ATOM_LOCALNAME__62_67_73_6F_75_6E_64 } ;
("bias") => { $ crate :: ATOM_LOCALNAME__62_69_61_73 } ;
("big") => { $ crate :: ATOM_LOCALNAME__62_69_67 } ;
("blink") => { $ crate :: ATOM_LOCALNAME__62_6C_69_6E_6B } ;
("body") => { $ crate :: ATOM_LOCALNAME__62_6F_64_79 } ;
("border") => { $ crate :: ATOM_LOCALNAME__62_6F_72_64_65_72 } ;
("br") => { $ crate :: ATOM_LOCALNAME__62_72 } ;
("button") => { $ crate :: ATOM_LOCALNAME__62_75_74_74_6F_6E } ;
("bvar") => { $ crate :: ATOM_LOCALNAME__62_76_61_72 } ;
("by") => { $ crate :: ATOM_LOCALNAME__62_79 } ;
("canvas") => { $ crate :: ATOM_LOCALNAME__63_61_6E_76_61_73 } ;
("caption") => { $ crate :: ATOM_LOCALNAME__63_61_70_74_69_6F_6E } ;
("card") => { $ crate :: ATOM_LOCALNAME__63_61_72_64 } ;
("ceiling") => { $ crate :: ATOM_LOCALNAME__63_65_69_6C_69_6E_67 } ;
("center") => { $ crate :: ATOM_LOCALNAME__63_65_6E_74_65_72 } ;
("char") => { $ crate :: ATOM_LOCALNAME__63_68_61_72 } ;
("charoff") => { $ crate :: ATOM_LOCALNAME__63_68_61_72_6F_66_66 } ;
("charset") => { $ crate :: ATOM_LOCALNAME__63_68_61_72_73_65_74 } ;
("checked") => { $ crate :: ATOM_LOCALNAME__63_68_65_63_6B_65_64 } ;
("ci") => { $ crate :: ATOM_LOCALNAME__63_69 } ;
("circle") => { $ crate :: ATOM_LOCALNAME__63_69_72_63_6C_65 } ;
("cite") => { $ crate :: ATOM_LOCALNAME__63_69_74_65 } ;
("class") => { $ crate :: ATOM_LOCALNAME__63_6C_61_73_73 } ;
("classid") => { $ crate :: ATOM_LOCALNAME__63_6C_61_73_73_69_64 } ;
("clear") => { $ crate :: ATOM_LOCALNAME__63_6C_65_61_72 } ;
("clip") => { $ crate :: ATOM_LOCALNAME__63_6C_69_70 } ;
("close") => { $ crate :: ATOM_LOCALNAME__63_6C_6F_73_65 } ;
("closure") => { $ crate :: ATOM_LOCALNAME__63_6C_6F_73_75_72_65 } ;
("cn") => { $ crate :: ATOM_LOCALNAME__63_6E } ;
("code") => { $ crate :: ATOM_LOCALNAME__63_6F_64_65 } ;
("col") => { $ crate :: ATOM_LOCALNAME__63_6F_6C } ;
("color") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_6F_72 } ;
("cols") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_73 } ;
("colspan") => { $ crate :: ATOM_LOCALNAME__63_6F_6C_73_70_61_6E } ;
("compact") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_61_63_74 } ;
("compose") => { $ crate :: ATOM_LOCALNAME__63_6F_6D_70_6F_73_65 } ;
("content") => { $ crate :: ATOM_LOCALNAME__63_6F_6E_74_65_6E_74 } ;
("coords") => { $ crate :: ATOM_LOCALNAME__63_6F_6F_72_64_73 } ;
("cos") => { $ crate :: ATOM_LOCALNAME__63_6F_73 } ;
("cosh") => { $ crate :: ATOM_LOCALNAME__63_6F_73_68 } ;
("cot") => { $ crate :: ATOM_LOCALNAME__63_6F_74 } ;
("coth") => { $ crate :: ATOM_LOCALNAME__63_6F_74_68 } ;
("csc") => { $ crate :: ATOM_LOCALNAME__63_73_63 } ;
("csch") => { $ crate :: ATOM_LOCALNAME__63_73_63_68 } ;
("csymbol") => { $ crate :: ATOM_LOCALNAME__63_73_79_6D_62_6F_6C } ;
("curl") => { $ crate :: ATOM_LOCALNAME__63_75_72_6C } ;
("cursor") => { $ crate :: ATOM_LOCALNAME__63_75_72_73_6F_72 } ;
("cx") => { $ crate :: ATOM_LOCALNAME__63_78 } ;
("cy") => { $ crate :: ATOM_LOCALNAME__63_79 } ;
("d") => { $ crate :: ATOM_LOCALNAME__64 } ;
("data") => { $ crate :: ATOM_LOCALNAME__64_61_74_61 } ;
("datafld") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_66_6C_64 } ;
("datasrc") => { $ crate :: ATOM_LOCALNAME__64_61_74_61_73_72_63 } ;
("dd") => { $ crate :: ATOM_LOCALNAME__64_64 } ;
("declare") => { $ crate :: ATOM_LOCALNAME__64_65_63_6C_61_72_65 } ;
("default") => { $ crate :: ATOM_LOCALNAME__64_65_66_61_75_6C_74 } ;
("defer") => { $ crate :: ATOM_LOCALNAME__64_65_66_65_72 } ;
("defs") => { $ crate :: ATOM_LOCALNAME__64_65_66_73 } ;
("degree") => { $ crate :: ATOM_LOCALNAME__64_65_67_72_65_65 } ;
("del") => { $ crate :: ATOM_LOCALNAME__64_65_6C } ;
("depth") => { $ crate :: ATOM_LOCALNAME__64_65_70_74_68 } ;
("desc") => { $ crate :: ATOM_LOCALNAME__64_65_73_63 } ;
("descent") => { $ crate :: ATOM_LOCALNAME__64_65_73_63_65_6E_74 } ;
("details") => { $ crate :: ATOM_LOCALNAME__64_65_74_61_69_6C_73 } ;
("dfn") => { $ crate :: ATOM_LOCALNAME__64_66_6E } ;
("dialog") => { $ crate :: ATOM_LOCALNAME__64_69_61_6C_6F_67 } ;
("diff") => { $ crate :: ATOM_LOCALNAME__64_69_66_66 } ;
("dir") => { $ crate :: ATOM_LOCALNAME__64_69_72 } ;
("dirname") => { $ crate :: ATOM_LOCALNAME__64_69_72_6E_61_6D_65 } ;
("discard") => { $ crate :: ATOM_LOCALNAME__64_69_73_63_61_72_64 } ;
("display") => { $ crate :: ATOM_LOCALNAME__64_69_73_70_6C_61_79 } ;
("div") => { $ crate :: ATOM_LOCALNAME__64_69_76 } ;
("divide") => { $ crate :: ATOM_LOCALNAME__64_69_76_69_64_65 } ;
("divisor") => { $ crate :: ATOM_LOCALNAME__64_69_76_69_73_6F_72 } ;
("dl") => { $ crate :: ATOM_LOCALNAME__64_6C } ;
("domain") => { $ crate :: ATOM_LOCALNAME__64_6F_6D_61_69_6E } ;
("dt") => { $ crate :: ATOM_LOCALNAME__64_74 } ;
("dur") => { $ crate :: ATOM_LOCALNAME__64_75_72 } ;
("dx") => { $ crate :: ATOM_LOCALNAME__64_78 } ;
("dy") => { $ crate :: ATOM_LOCALNAME__64_79 } ;
("edge") => { $ crate :: ATOM_LOCALNAME__65_64_67_65 } ;
("ellipse") => { $ crate :: ATOM_LOCALNAME__65_6C_6C_69_70_73_65 } ;
("em") => { $ crate :: ATOM_LOCALNAME__65_6D } ;
("embed") => { $ crate :: ATOM_LOCALNAME__65_6D_62_65_64 } ;
("enctype") => { $ crate :: ATOM_LOCALNAME__65_6E_63_74_79_70_65 } ;
("end") => { $ crate :: ATOM_LOCALNAME__65_6E_64 } ;
("eq") => { $ crate :: ATOM_LOCALNAME__65_71 } ;
("event") => { $ crate :: ATOM_LOCALNAME__65_76_65_6E_74 } ;
("exists") => { $ crate :: ATOM_LOCALNAME__65_78_69_73_74_73 } ;
("exp") => { $ crate :: ATOM_LOCALNAME__65_78_70 } ;
("face") => { $ crate :: ATOM_LOCALNAME__66_61_63_65 } ;
("false") => { $ crate :: ATOM_LOCALNAME__66_61_6C_73_65 } ;
("feBlend") => { $ crate :: ATOM_LOCALNAME__66_65_42_6C_65_6E_64 } ;
("feFlood") => { $ crate :: ATOM_LOCALNAME__66_65_46_6C_6F_6F_64 } ;
("feFuncA") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_41 } ;
("feFuncB") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_42 } ;
("feFuncG") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_47 } ;
("feFuncR") => { $ crate :: ATOM_LOCALNAME__66_65_46_75_6E_63_52 } ;
("feImage") => { $ crate :: ATOM_LOCALNAME__66_65_49_6D_61_67_65 } ;
("feMerge") => { $ crate :: ATOM_LOCALNAME__66_65_4D_65_72_67_65 } ;
("feTile") => { $ crate :: ATOM_LOCALNAME__66_65_54_69_6C_65 } ;
("feblend") => { $ crate :: ATOM_LOCALNAME__66_65_62_6C_65_6E_64 } ;
("feflood") => { $ crate :: ATOM_LOCALNAME__66_65_66_6C_6F_6F_64 } ;
("fefunca") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_61 } ;
("fefuncb") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_62 } ;
("fefuncg") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_67 } ;
("fefuncr") => { $ crate :: ATOM_LOCALNAME__66_65_66_75_6E_63_72 } ;
("feimage") => { $ crate :: ATOM_LOCALNAME__66_65_69_6D_61_67_65 } ;
("femerge") => { $ crate :: ATOM_LOCALNAME__66_65_6D_65_72_67_65 } ;
("fence") => { $ crate :: ATOM_LOCALNAME__66_65_6E_63_65 } ;
("fetch") => { $ crate :: ATOM_LOCALNAME__66_65_74_63_68 } ;
("fetile") => { $ crate :: ATOM_LOCALNAME__66_65_74_69_6C_65 } ;
("figure") => { $ crate :: ATOM_LOCALNAME__66_69_67_75_72_65 } ;
("fill") => { $ crate :: ATOM_LOCALNAME__66_69_6C_6C } ;
("filter") => { $ crate :: ATOM_LOCALNAME__66_69_6C_74_65_72 } ;
("floor") => { $ crate :: ATOM_LOCALNAME__66_6C_6F_6F_72 } ;
("fn") => { $ crate :: ATOM_LOCALNAME__66_6E } ;
("font") => { $ crate :: ATOM_LOCALNAME__66_6F_6E_74 } ;
("footer") => { $ crate :: ATOM_LOCALNAME__66_6F_6F_74_65_72 } ;
("for") => { $ crate :: ATOM_LOCALNAME__66_6F_72 } ;
("forall") => { $ crate :: ATOM_LOCALNAME__66_6F_72_61_6C_6C } ;
("form") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D } ;
("format") => { $ crate :: ATOM_LOCALNAME__66_6F_72_6D_61_74 } ;
("frame") => { $ crate :: ATOM_LOCALNAME__66_72_61_6D_65 } ;
("from") => { $ crate :: ATOM_LOCALNAME__66_72_6F_6D } ;
("fx") => { $ crate :: ATOM_LOCALNAME__66_78 } ;
("fy") => { $ crate :: ATOM_LOCALNAME__66_79 } ;
("g") => { $ crate :: ATOM_LOCALNAME__67 } ;
("g1") => { $ crate :: ATOM_LOCALNAME__67_31 } ;
("g2") => { $ crate :: ATOM_LOCALNAME__67_32 } ;
("gcd") => { $ crate :: ATOM_LOCALNAME__67_63_64 } ;
("geq") => { $ crate :: ATOM_LOCALNAME__67_65_71 } ;
("glyph") => { $ crate :: ATOM_LOCALNAME__67_6C_79_70_68 } ;
("grad") => { $ crate :: ATOM_LOCALNAME__67_72_61_64 } ;
("gt") => { $ crate :: ATOM_LOCALNAME__67_74 } ;
("h1") => { $ crate :: ATOM_LOCALNAME__68_31 } ;
("h2") => { $ crate :: ATOM_LOCALNAME__68_32 } ;
("h3") => { $ crate :: ATOM_LOCALNAME__68_33 } ;
("h4") => { $ crate :: ATOM_LOCALNAME__68_34 } ;
("h5") => { $ crate :: ATOM_LOCALNAME__68_35 } ;
("h6") => { $ crate :: ATOM_LOCALNAME__68_36 } ;
("handler") => { $ crate :: ATOM_LOCALNAME__68_61_6E_64_6C_65_72 } ;
("hanging") => { $ crate :: ATOM_LOCALNAME__68_61_6E_67_69_6E_67 } ;
("head") => { $ crate :: ATOM_LOCALNAME__68_65_61_64 } ;
("header") => { $ crate :: ATOM_LOCALNAME__68_65_61_64_65_72 } ;
("headers") => { $ crate :: ATOM_LOCALNAME__68_65_61_64_65_72_73 } ;
("height") => { $ crate :: ATOM_LOCALNAME__68_65_69_67_68_74 } ;
("hgroup") => { $ crate :: ATOM_LOCALNAME__68_67_72_6F_75_70 } ;
("hidden") => { $ crate :: ATOM_LOCALNAME__68_69_64_64_65_6E } ;
("high") => { $ crate :: ATOM_LOCALNAME__68_69_67_68 } ;
("hkern") => { $ crate :: ATOM_LOCALNAME__68_6B_65_72_6E } ;
("hr") => { $ crate :: ATOM_LOCALNAME__68_72 } ;
("href") => { $ crate :: ATOM_LOCALNAME__68_72_65_66 } ;
("hspace") => { $ crate :: ATOM_LOCALNAME__68_73_70_61_63_65 } ;
("html") => { $ crate :: ATOM_LOCALNAME__68_74_6D_6C } ;
("i") => { $ crate :: ATOM_LOCALNAME__69 } ;
("icon") => { $ crate :: ATOM_LOCALNAME__69_63_6F_6E } ;
("id") => { $ crate :: ATOM_LOCALNAME__69_64 } ;
("ident") => { $ crate :: ATOM_LOCALNAME__69_64_65_6E_74 } ;
("iframe") => { $ crate :: ATOM_LOCALNAME__69_66_72_61_6D_65 } ;
("image") => { $ crate :: ATOM_LOCALNAME__69_6D_61_67_65 } ;
("img") => { $ crate :: ATOM_LOCALNAME__69_6D_67 } ;
("implies") => { $ crate :: ATOM_LOCALNAME__69_6D_70_6C_69_65_73 } ;
("in") => { $ crate :: ATOM_LOCALNAME__69_6E } ;
("in2") => { $ crate :: ATOM_LOCALNAME__69_6E_32 } ;
("index") => { $ crate :: ATOM_LOCALNAME__69_6E_64_65_78 } ;
("input") => { $ crate :: ATOM_LOCALNAME__69_6E_70_75_74 } ;
("ins") => { $ crate :: ATOM_LOCALNAME__69_6E_73 } ;
("int") => { $ crate :: ATOM_LOCALNAME__69_6E_74 } ;
("inverse") => { $ crate :: ATOM_LOCALNAME__69_6E_76_65_72_73_65 } ;
("isindex") => { $ crate :: ATOM_LOCALNAME__69_73_69_6E_64_65_78 } ;
("ismap") => { $ crate :: ATOM_LOCALNAME__69_73_6D_61_70 } ;
("itemid") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_69_64 } ;
("itemref") => { $ crate :: ATOM_LOCALNAME__69_74_65_6D_72_65_66 } ;
("k") => { $ crate :: ATOM_LOCALNAME__6B } ;
("k1") => { $ crate :: ATOM_LOCALNAME__6B_31 } ;
("k2") => { $ crate :: ATOM_LOCALNAME__6B_32 } ;
("k3") => { $ crate :: ATOM_LOCALNAME__6B_33 } ;
("k4") => { $ crate :: ATOM_LOCALNAME__6B_34 } ;
("kbd") => { $ crate :: ATOM_LOCALNAME__6B_62_64 } ;
("kerning") => { $ crate :: ATOM_LOCALNAME__6B_65_72_6E_69_6E_67 } ;
("keygen") => { $ crate :: ATOM_LOCALNAME__6B_65_79_67_65_6E } ;
("kind") => { $ crate :: ATOM_LOCALNAME__6B_69_6E_64 } ;
("label") => { $ crate :: ATOM_LOCALNAME__6C_61_62_65_6C } ;
("lambda") => { $ crate :: ATOM_LOCALNAME__6C_61_6D_62_64_61 } ;
("lang") => { $ crate :: ATOM_LOCALNAME__6C_61_6E_67 } ;
("largeop") => { $ crate :: ATOM_LOCALNAME__6C_61_72_67_65_6F_70 } ;
("lcm") => { $ crate :: ATOM_LOCALNAME__6C_63_6D } ;
("legend") => { $ crate :: ATOM_LOCALNAME__6C_65_67_65_6E_64 } ;
("leq") => { $ crate :: ATOM_LOCALNAME__6C_65_71 } ;
("li") => { $ crate :: ATOM_LOCALNAME__6C_69 } ;
("limit") => { $ crate :: ATOM_LOCALNAME__6C_69_6D_69_74 } ;
("line") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_65 } ;
("link") => { $ crate :: ATOM_LOCALNAME__6C_69_6E_6B } ;
("list") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74 } ;
("listing") => { $ crate :: ATOM_LOCALNAME__6C_69_73_74_69_6E_67 } ;
("ln") => { $ crate :: ATOM_LOCALNAME__6C_6E } ;
("local") => { $ crate :: ATOM_LOCALNAME__6C_6F_63_61_6C } ;
("log") => { $ crate :: ATOM_LOCALNAME__6C_6F_67 } ;
("logbase") => { $ crate :: ATOM_LOCALNAME__6C_6F_67_62_61_73_65 } ;
("loop") => { $ crate :: ATOM_LOCALNAME__6C_6F_6F_70 } ;
("low") => { $ crate :: ATOM_LOCALNAME__6C_6F_77 } ;
("lowsrc") => { $ crate :: ATOM_LOCALNAME__6C_6F_77_73_72_63 } ;
("lquote") => { $ crate :: ATOM_LOCALNAME__6C_71_75_6F_74_65 } ;
("lspace") => { $ crate :: ATOM_LOCALNAME__6C_73_70_61_63_65 } ;
("lt") => { $ crate :: ATOM_LOCALNAME__6C_74 } ;
("macros") => { $ crate :: ATOM_LOCALNAME__6D_61_63_72_6F_73 } ;
("maction") => { $ crate :: ATOM_LOCALNAME__6D_61_63_74_69_6F_6E } ;
("main") => { $ crate :: ATOM_LOCALNAME__6D_61_69_6E } ;
("map") => { $ crate :: ATOM_LOCALNAME__6D_61_70 } ;
("mark") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B } ;
("marker") => { $ crate :: ATOM_LOCALNAME__6D_61_72_6B_65_72 } ;
("marquee") => { $ crate :: ATOM_LOCALNAME__6D_61_72_71_75_65_65 } ;
("mask") => { $ crate :: ATOM_LOCALNAME__6D_61_73_6B } ;
("math") => { $ crate :: ATOM_LOCALNAME__6D_61_74_68 } ;
("matrix") => { $ crate :: ATOM_LOCALNAME__6D_61_74_72_69_78 } ;
("max") => { $ crate :: ATOM_LOCALNAME__6D_61_78 } ;
("maxsize") => { $ crate :: ATOM_LOCALNAME__6D_61_78_73_69_7A_65 } ;
("mean") => { $ crate :: ATOM_LOCALNAME__6D_65_61_6E } ;
("media") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_61 } ;
("median") => { $ crate :: ATOM_LOCALNAME__6D_65_64_69_61_6E } ;
("menu") => { $ crate :: ATOM_LOCALNAME__6D_65_6E_75 } ;
("merror") => { $ crate :: ATOM_LOCALNAME__6D_65_72_72_6F_72 } ;
("meta") => { $ crate :: ATOM_LOCALNAME__6D_65_74_61 } ;
("meter") => { $ crate :: ATOM_LOCALNAME__6D_65_74_65_72 } ;
("method") => { $ crate :: ATOM_LOCALNAME__6D_65_74_68_6F_64 } ;
("mfenced") => { $ crate :: ATOM_LOCALNAME__6D_66_65_6E_63_65_64 } ;
("mfrac") => { $ crate :: ATOM_LOCALNAME__6D_66_72_61_63 } ;
("mglyph") => { $ crate :: ATOM_LOCALNAME__6D_67_6C_79_70_68 } ;
("mi") => { $ crate :: ATOM_LOCALNAME__6D_69 } ;
("min") => { $ crate :: ATOM_LOCALNAME__6D_69_6E } ;
("minsize") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_73_69_7A_65 } ;
("minus") => { $ crate :: ATOM_LOCALNAME__6D_69_6E_75_73 } ;
("mn") => { $ crate :: ATOM_LOCALNAME__6D_6E } ;
("mo") => { $ crate :: ATOM_LOCALNAME__6D_6F } ;
("mode") => { $ crate :: ATOM_LOCALNAME__6D_6F_64_65 } ;
("moment") => { $ crate :: ATOM_LOCALNAME__6D_6F_6D_65_6E_74 } ;
("mover") => { $ crate :: ATOM_LOCALNAME__6D_6F_76_65_72 } ;
("mpadded") => { $ crate :: ATOM_LOCALNAME__6D_70_61_64_64_65_64 } ;
("mpath") => { $ crate :: ATOM_LOCALNAME__6D_70_61_74_68 } ;
("mroot") => { $ crate :: ATOM_LOCALNAME__6D_72_6F_6F_74 } ;
("mrow") => { $ crate :: ATOM_LOCALNAME__6D_72_6F_77 } ;
("ms") => { $ crate :: ATOM_LOCALNAME__6D_73 } ;
("mspace") => { $ crate :: ATOM_LOCALNAME__6D_73_70_61_63_65 } ;
("msqrt") => { $ crate :: ATOM_LOCALNAME__6D_73_71_72_74 } ;
("mstyle") => { $ crate :: ATOM_LOCALNAME__6D_73_74_79_6C_65 } ;
("msub") => { $ crate :: ATOM_LOCALNAME__6D_73_75_62 } ;
("msubsup") => { $ crate :: ATOM_LOCALNAME__6D_73_75_62_73_75_70 } ;
("msup") => { $ crate :: ATOM_LOCALNAME__6D_73_75_70 } ;
("mtable") => { $ crate :: ATOM_LOCALNAME__6D_74_61_62_6C_65 } ;
("mtd") => { $ crate :: ATOM_LOCALNAME__6D_74_64 } ;
("mtext") => { $ crate :: ATOM_LOCALNAME__6D_74_65_78_74 } ;
("mtr") => { $ crate :: ATOM_LOCALNAME__6D_74_72 } ;
("munder") => { $ crate :: ATOM_LOCALNAME__6D_75_6E_64_65_72 } ;
("muted") => { $ crate :: ATOM_LOCALNAME__6D_75_74_65_64 } ;
("name") => { $ crate :: ATOM_LOCALNAME__6E_61_6D_65 } ;
("nargs") => { $ crate :: ATOM_LOCALNAME__6E_61_72_67_73 } ;
("nav") => { $ crate :: ATOM_LOCALNAME__6E_61_76 } ;
("neq") => { $ crate :: ATOM_LOCALNAME__6E_65_71 } ;
("nest") => { $ crate :: ATOM_LOCALNAME__6E_65_73_74 } ;
("nextid") => { $ crate :: ATOM_LOCALNAME__6E_65_78_74_69_64 } ;
("nobr") => { $ crate :: ATOM_LOCALNAME__6E_6F_62_72 } ;
("noembed") => { $ crate :: ATOM_LOCALNAME__6E_6F_65_6D_62_65_64 } ;
("nohref") => { $ crate :: ATOM_LOCALNAME__6E_6F_68_72_65_66 } ;
("nonce") => { $ crate :: ATOM_LOCALNAME__6E_6F_6E_63_65 } ;
("none") => { $ crate :: ATOM_LOCALNAME__6E_6F_6E_65 } ;
("noshade") => { $ crate :: ATOM_LOCALNAME__6E_6F_73_68_61_64_65 } ;
("not") => { $ crate :: ATOM_LOCALNAME__6E_6F_74 } ;
("notin") => { $ crate :: ATOM_LOCALNAME__6E_6F_74_69_6E } ;
("nowrap") => { $ crate :: ATOM_LOCALNAME__6E_6F_77_72_61_70 } ;
("object") => { $ crate :: ATOM_LOCALNAME__6F_62_6A_65_63_74 } ;
("offset") => { $ crate :: ATOM_LOCALNAME__6F_66_66_73_65_74 } ;
("ol") => { $ crate :: ATOM_LOCALNAME__6F_6C } ;
("onabort") => { $ crate :: ATOM_LOCALNAME__6F_6E_61_62_6F_72_74 } ;
("onbegin") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_65_67_69_6E } ;
("onblur") => { $ crate :: ATOM_LOCALNAME__6F_6E_62_6C_75_72 } ;
("onclick") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6C_69_63_6B } ;
("oncopy") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_6F_70_79 } ;
("oncut") => { $ crate :: ATOM_LOCALNAME__6F_6E_63_75_74 } ;
("ondrag") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_61_67 } ;
("ondrop") => { $ crate :: ATOM_LOCALNAME__6F_6E_64_72_6F_70 } ;
("onend") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_6E_64 } ;
("onerror") => { $ crate :: ATOM_LOCALNAME__6F_6E_65_72_72_6F_72 } ;
("onfocus") => { $ crate :: ATOM_LOCALNAME__6F_6E_66_6F_63_75_73 } ;
("onhelp") => { $ crate :: ATOM_LOCALNAME__6F_6E_68_65_6C_70 } ;
("oninput") => { $ crate :: ATOM_LOCALNAME__6F_6E_69_6E_70_75_74 } ;
("onkeyup") => { $ crate :: ATOM_LOCALNAME__6F_6E_6B_65_79_75_70 } ;
("onload") => { $ crate :: ATOM_LOCALNAME__6F_6E_6C_6F_61_64 } ;
("onmove") => { $ crate :: ATOM_LOCALNAME__6F_6E_6D_6F_76_65 } ;
("onpaste") => { $ crate :: ATOM_LOCALNAME__6F_6E_70_61_73_74_65 } ;
("onreset") => { $ crate :: ATOM_LOCALNAME__6F_6E_72_65_73_65_74 } ;
("onstart") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_61_72_74 } ;
("onstop") => { $ crate :: ATOM_LOCALNAME__6F_6E_73_74_6F_70 } ;
("onzoom") => { $ crate :: ATOM_LOCALNAME__6F_6E_7A_6F_6F_6D } ;
("opacity") => { $ crate :: ATOM_LOCALNAME__6F_70_61_63_69_74_79 } ;
("open") => { $ crate :: ATOM_LOCALNAME__6F_70_65_6E } ;
("optimum") => { $ crate :: ATOM_LOCALNAME__6F_70_74_69_6D_75_6D } ;
("option") => { $ crate :: ATOM_LOCALNAME__6F_70_74_69_6F_6E } ;
("or") => { $ crate :: ATOM_LOCALNAME__6F_72 } ;
("order") => { $ crate :: ATOM_LOCALNAME__6F_72_64_65_72 } ;
("orient") => { $ crate :: ATOM_LOCALNAME__6F_72_69_65_6E_74 } ;
("origin") => { $ crate :: ATOM_LOCALNAME__6F_72_69_67_69_6E } ;
("other") => { $ crate :: ATOM_LOCALNAME__6F_74_68_65_72 } ;
("output") => { $ crate :: ATOM_LOCALNAME__6F_75_74_70_75_74 } ;
("p") => { $ crate :: ATOM_LOCALNAME__70 } ;
("param") => { $ crate :: ATOM_LOCALNAME__70_61_72_61_6D } ;
("parse") => { $ crate :: ATOM_LOCALNAME__70_61_72_73_65 } ;
("path") => { $ crate :: ATOM_LOCALNAME__70_61_74_68 } ;
("pattern") => { $ crate :: ATOM_LOCALNAME__70_61_74_74_65_72_6E } ;
("pi") => { $ crate :: ATOM_LOCALNAME__70_69 } ;
("picture") => { $ crate :: ATOM_LOCALNAME__70_69_63_74_75_72_65 } ;
("piece") => { $ crate :: ATOM_LOCALNAME__70_69_65_63_65 } ;
("ping") => { $ crate :: ATOM_LOCALNAME__70_69_6E_67 } ;
("plus") => { $ crate :: ATOM_LOCALNAME__70_6C_75_73 } ;
("points") => { $ crate :: ATOM_LOCALNAME__70_6F_69_6E_74_73 } ;
("polygon") => { $ crate :: ATOM_LOCALNAME__70_6F_6C_79_67_6F_6E } ;
("poster") => { $ crate :: ATOM_LOCALNAME__70_6F_73_74_65_72 } ;
("power") => { $ crate :: ATOM_LOCALNAME__70_6F_77_65_72 } ;
("pre") => { $ crate :: ATOM_LOCALNAME__70_72_65 } ;
("preload") => { $ crate :: ATOM_LOCALNAME__70_72_65_6C_6F_61_64 } ;
("primes") => { $ crate :: ATOM_LOCALNAME__70_72_69_6D_65_73 } ;
("product") => { $ crate :: ATOM_LOCALNAME__70_72_6F_64_75_63_74 } ;
("profile") => { $ crate :: ATOM_LOCALNAME__70_72_6F_66_69_6C_65 } ;
("prompt") => { $ crate :: ATOM_LOCALNAME__70_72_6F_6D_70_74 } ;
("q") => { $ crate :: ATOM_LOCALNAME__71 } ;
("r") => { $ crate :: ATOM_LOCALNAME__72 } ;
("radius") => { $ crate :: ATOM_LOCALNAME__72_61_64_69_75_73 } ;
("rb") => { $ crate :: ATOM_LOCALNAME__72_62 } ;
("real") => { $ crate :: ATOM_LOCALNAME__72_65_61_6C } ;
("reals") => { $ crate :: ATOM_LOCALNAME__72_65_61_6C_73 } ;
("rect") => { $ crate :: ATOM_LOCALNAME__72_65_63_74 } ;
("refX") => { $ crate :: ATOM_LOCALNAME__72_65_66_58 } ;
("refY") => { $ crate :: ATOM_LOCALNAME__72_65_66_59 } ;
("refx") => { $ crate :: ATOM_LOCALNAME__72_65_66_78 } ;
("refy") => { $ crate :: ATOM_LOCALNAME__72_65_66_79 } ;
("rel") => { $ crate :: ATOM_LOCALNAME__72_65_6C } ;
("reln") => { $ crate :: ATOM_LOCALNAME__72_65_6C_6E } ;
("rem") => { $ crate :: ATOM_LOCALNAME__72_65_6D } ;
("repeat") => { $ crate :: ATOM_LOCALNAME__72_65_70_65_61_74 } ;
("replace") => { $ crate :: ATOM_LOCALNAME__72_65_70_6C_61_63_65 } ;
("restart") => { $ crate :: ATOM_LOCALNAME__72_65_73_74_61_72_74 } ;
("result") => { $ crate :: ATOM_LOCALNAME__72_65_73_75_6C_74 } ;
("rev") => { $ crate :: ATOM_LOCALNAME__72_65_76 } ;
("role") => { $ crate :: ATOM_LOCALNAME__72_6F_6C_65 } ;
("root") => { $ crate :: ATOM_LOCALNAME__72_6F_6F_74 } ;
("rotate") => { $ crate :: ATOM_LOCALNAME__72_6F_74_61_74_65 } ;
("rows") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73 } ;
("rowspan") => { $ crate :: ATOM_LOCALNAME__72_6F_77_73_70_61_6E } ;
("rp") => { $ crate :: ATOM_LOCALNAME__72_70 } ;
("rquote") => { $ crate :: ATOM_LOCALNAME__72_71_75_6F_74_65 } ;
("rspace") => { $ crate :: ATOM_LOCALNAME__72_73_70_61_63_65 } ;
("rt") => { $ crate :: ATOM_LOCALNAME__72_74 } ;
("rtc") => { $ crate :: ATOM_LOCALNAME__72_74_63 } ;
("ruby") => { $ crate :: ATOM_LOCALNAME__72_75_62_79 } ;
("rule") => { $ crate :: ATOM_LOCALNAME__72_75_6C_65 } ;
("rules") => { $ crate :: ATOM_LOCALNAME__72_75_6C_65_73 } ;
("rx") => { $ crate :: ATOM_LOCALNAME__72_78 } ;
("ry") => { $ crate :: ATOM_LOCALNAME__72_79 } ;
("s") => { $ crate :: ATOM_LOCALNAME__73 } ;
("samp") => { $ crate :: ATOM_LOCALNAME__73_61_6D_70 } ;
("sandbox") => { $ crate :: ATOM_LOCALNAME__73_61_6E_64_62_6F_78 } ;
("scale") => { $ crate :: ATOM_LOCALNAME__73_63_61_6C_65 } ;
("scheme") => { $ crate :: ATOM_LOCALNAME__73_63_68_65_6D_65 } ;
("scope") => { $ crate :: ATOM_LOCALNAME__73_63_6F_70_65 } ;
("scoped") => { $ crate :: ATOM_LOCALNAME__73_63_6F_70_65_64 } ;
("script") => { $ crate :: ATOM_LOCALNAME__73_63_72_69_70_74 } ;
("sdev") => { $ crate :: ATOM_LOCALNAME__73_64_65_76 } ;
("sec") => { $ crate :: ATOM_LOCALNAME__73_65_63 } ;
("sech") => { $ crate :: ATOM_LOCALNAME__73_65_63_68 } ;
("section") => { $ crate :: ATOM_LOCALNAME__73_65_63_74_69_6F_6E } ;
("seed") => { $ crate :: ATOM_LOCALNAME__73_65_65_64 } ;
("select") => { $ crate :: ATOM_LOCALNAME__73_65_6C_65_63_74 } ;
("sep") => { $ crate :: ATOM_LOCALNAME__73_65_70 } ;
("set") => { $ crate :: ATOM_LOCALNAME__73_65_74 } ;
("setdiff") => { $ crate :: ATOM_LOCALNAME__73_65_74_64_69_66_66 } ;
("shape") => { $ crate :: ATOM_LOCALNAME__73_68_61_70_65 } ;
("show") => { $ crate :: ATOM_LOCALNAME__73_68_6F_77 } ;
("sin") => { $ crate :: ATOM_LOCALNAME__73_69_6E } ;
("sinh") => { $ crate :: ATOM_LOCALNAME__73_69_6E_68 } ;
("size") => { $ crate :: ATOM_LOCALNAME__73_69_7A_65 } ;
("sizes") => { $ crate :: ATOM_LOCALNAME__73_69_7A_65_73 } ;
("slope") => { $ crate :: ATOM_LOCALNAME__73_6C_6F_70_65 } ;
("slot") => { $ crate :: ATOM_LOCALNAME__73_6C_6F_74 } ;
("small") => { $ crate :: ATOM_LOCALNAME__73_6D_61_6C_6C } ;
("source") => { $ crate :: ATOM_LOCALNAME__73_6F_75_72_63_65 } ;
("space") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_65 } ;
("spacer") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_65_72 } ;
("spacing") => { $ crate :: ATOM_LOCALNAME__73_70_61_63_69_6E_67 } ;
("span") => { $ crate :: ATOM_LOCALNAME__73_70_61_6E } ;
("speed") => { $ crate :: ATOM_LOCALNAME__73_70_65_65_64 } ;
("src") => { $ crate :: ATOM_LOCALNAME__73_72_63 } ;
("srcdoc") => { $ crate :: ATOM_LOCALNAME__73_72_63_64_6F_63 } ;
("srclang") => { $ crate :: ATOM_LOCALNAME__73_72_63_6C_61_6E_67 } ;
("srcset") => { $ crate :: ATOM_LOCALNAME__73_72_63_73_65_74 } ;
("standby") => { $ crate :: ATOM_LOCALNAME__73_74_61_6E_64_62_79 } ;
("start") => { $ crate :: ATOM_LOCALNAME__73_74_61_72_74 } ;
("stemh") => { $ crate :: ATOM_LOCALNAME__73_74_65_6D_68 } ;
("stemv") => { $ crate :: ATOM_LOCALNAME__73_74_65_6D_76 } ;
("step") => { $ crate :: ATOM_LOCALNAME__73_74_65_70 } ;
("stop") => { $ crate :: ATOM_LOCALNAME__73_74_6F_70 } ;
("strike") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6B_65 } ;
("string") => { $ crate :: ATOM_LOCALNAME__73_74_72_69_6E_67 } ;
("stroke") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6B_65 } ;
("strong") => { $ crate :: ATOM_LOCALNAME__73_74_72_6F_6E_67 } ;
("style") => { $ crate :: ATOM_LOCALNAME__73_74_79_6C_65 } ;
("sub") => { $ crate :: ATOM_LOCALNAME__73_75_62 } ;
("subset") => { $ crate :: ATOM_LOCALNAME__73_75_62_73_65_74 } ;
("sum") => { $ crate :: ATOM_LOCALNAME__73_75_6D } ;
("summary") => { $ crate :: ATOM_LOCALNAME__73_75_6D_6D_61_72_79 } ;
("sup") => { $ crate :: ATOM_LOCALNAME__73_75_70 } ;
("svg") => { $ crate :: ATOM_LOCALNAME__73_76_67 } ;
("switch") => { $ crate :: ATOM_LOCALNAME__73_77_69_74_63_68 } ;
("symbol") => { $ crate :: ATOM_LOCALNAME__73_79_6D_62_6F_6C } ;
("table") => { $ crate :: ATOM_LOCALNAME__74_61_62_6C_65 } ;
("tan") => { $ crate :: ATOM_LOCALNAME__74_61_6E } ;
("tanh") => { $ crate :: ATOM_LOCALNAME__74_61_6E_68 } ;
("target") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74 } ;
("targetX") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_58 } ;
("targetY") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_59 } ;
("targetx") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_78 } ;
("targety") => { $ crate :: ATOM_LOCALNAME__74_61_72_67_65_74_79 } ;
("tbody") => { $ crate :: ATOM_LOCALNAME__74_62_6F_64_79 } ;
("tbreak") => { $ crate :: ATOM_LOCALNAME__74_62_72_65_61_6B } ;
("td") => { $ crate :: ATOM_LOCALNAME__74_64 } ;
("tendsto") => { $ crate :: ATOM_LOCALNAME__74_65_6E_64_73_74_6F } ;
("text") => { $ crate :: ATOM_LOCALNAME__74_65_78_74 } ;
("tfoot") => { $ crate :: ATOM_LOCALNAME__74_66_6F_6F_74 } ;
("th") => { $ crate :: ATOM_LOCALNAME__74_68 } ;
("thead") => { $ crate :: ATOM_LOCALNAME__74_68_65_61_64 } ;
("time") => { $ crate :: ATOM_LOCALNAME__74_69_6D_65 } ;
("times") => { $ crate :: ATOM_LOCALNAME__74_69_6D_65_73 } ;
("title") => { $ crate :: ATOM_LOCALNAME__74_69_74_6C_65 } ;
("to") => { $ crate :: ATOM_LOCALNAME__74_6F } ;
("toggle") => { $ crate :: ATOM_LOCALNAME__74_6F_67_67_6C_65 } ;
("tr") => { $ crate :: ATOM_LOCALNAME__74_72 } ;
("track") => { $ crate :: ATOM_LOCALNAME__74_72_61_63_6B } ;
("tref") => { $ crate :: ATOM_LOCALNAME__74_72_65_66 } ;
("true") => { $ crate :: ATOM_LOCALNAME__74_72_75_65 } ;
("tspan") => { $ crate :: ATOM_LOCALNAME__74_73_70_61_6E } ;
("tt") => { $ crate :: ATOM_LOCALNAME__74_74 } ;
("type") => { $ crate :: ATOM_LOCALNAME__74_79_70_65 } ;
("u") => { $ crate :: ATOM_LOCALNAME__75 } ;
("u1") => { $ crate :: ATOM_LOCALNAME__75_31 } ;
("u2") => { $ crate :: ATOM_LOCALNAME__75_32 } ;
("ul") => { $ crate :: ATOM_LOCALNAME__75_6C } ;
("unicode") => { $ crate :: ATOM_LOCALNAME__75_6E_69_63_6F_64_65 } ;
("union") => { $ crate :: ATOM_LOCALNAME__75_6E_69_6F_6E } ;
("uplimit") => { $ crate :: ATOM_LOCALNAME__75_70_6C_69_6D_69_74 } ;
("use") => { $ crate :: ATOM_LOCALNAME__75_73_65 } ;
("usemap") => { $ crate :: ATOM_LOCALNAME__75_73_65_6D_61_70 } ;
("valign") => { $ crate :: ATOM_LOCALNAME__76_61_6C_69_67_6E } ;
("value") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65 } ;
("values") => { $ crate :: ATOM_LOCALNAME__76_61_6C_75_65_73 } ;
("var") => { $ crate :: ATOM_LOCALNAME__76_61_72 } ;
("vector") => { $ crate :: ATOM_LOCALNAME__76_65_63_74_6F_72 } ;
("version") => { $ crate :: ATOM_LOCALNAME__76_65_72_73_69_6F_6E } ;
("video") => { $ crate :: ATOM_LOCALNAME__76_69_64_65_6F } ;
("view") => { $ crate :: ATOM_LOCALNAME__76_69_65_77 } ;
("viewBox") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_42_6F_78 } ;
("viewbox") => { $ crate :: ATOM_LOCALNAME__76_69_65_77_62_6F_78 } ;
("vkern") => { $ crate :: ATOM_LOCALNAME__76_6B_65_72_6E } ;
("vlink") => { $ crate :: ATOM_LOCALNAME__76_6C_69_6E_6B } ;
("vspace") => { $ crate :: ATOM_LOCALNAME__76_73_70_61_63_65 } ;
("wbr") => { $ crate :: ATOM_LOCALNAME__77_62_72 } ;
("when") => { $ crate :: ATOM_LOCALNAME__77_68_65_6E } ;
("width") => { $ crate :: ATOM_LOCALNAME__77_69_64_74_68 } ;
("widths") => { $ crate :: ATOM_LOCALNAME__77_69_64_74_68_73 } ;
("wrap") => { $ crate :: ATOM_LOCALNAME__77_72_61_70 } ;
("x") => { $ crate :: ATOM_LOCALNAME__78 } ;
("x1") => { $ crate :: ATOM_LOCALNAME__78_31 } ;
("x2") => { $ crate :: ATOM_LOCALNAME__78_32 } ;
("xlink") => { $ crate :: ATOM_LOCALNAME__78_6C_69_6E_6B } ;
("xmlns") => { $ crate :: ATOM_LOCALNAME__78_6D_6C_6E_73 } ;
("xmp") => { $ crate :: ATOM_LOCALNAME__78_6D_70 } ;
("xor") => { $ crate :: ATOM_LOCALNAME__78_6F_72 } ;
("xref") => { $ crate :: ATOM_LOCALNAME__78_72_65_66 } ;
("y") => { $ crate :: ATOM_LOCALNAME__79 } ;
("y1") => { $ crate :: ATOM_LOCALNAME__79_31 } ;
("y2") => { $ crate :: ATOM_LOCALNAME__79_32 } ;
("z") => { $ crate :: ATOM_LOCALNAME__7A } ;
}pub type Prefix = :: string_cache :: Atom < PrefixStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct PrefixStaticSet ;
impl :: string_cache :: StaticAtomSet for PrefixStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 12913932095322966823u64 , disps : & [(0u32 , 0u32)] , atoms : & [""] , hashes : & [4082073077u32] } ;
& SET } fn empty_string_index () -> u32 { 0u32 } } pub const ATOM_PREFIX_ : Prefix = Prefix :: pack_static (0u32) ;
pub const ATOM_PREFIX__2A : Prefix = Prefix :: pack_inline (10752u64 , 1u8) ;
pub const ATOM_PREFIX__68_74_6D_6C : Prefix = Prefix :: pack_inline (465692813312u64 , 4u8) ;
pub const ATOM_PREFIX__6D_61_74_68_6D_6C : Prefix = Prefix :: pack_inline (30519592881319168u64 , 6u8) ;
pub const ATOM_PREFIX__73_76_67 : Prefix = Prefix :: pack_inline (1735815936u64 , 3u8) ;
pub const ATOM_PREFIX__78_6C_69_6E_6B : Prefix = Prefix :: pack_inline (118121959290880u64 , 5u8) ;
pub const ATOM_PREFIX__78_6D_6C : Prefix = Prefix :: pack_inline (1819113472u64 , 3u8) ;
pub const ATOM_PREFIX__78_6D_6C_6E_73 : Prefix = Prefix :: pack_inline (126918102710272u64 , 5u8) ;
# [doc = "Takes a namespace prefix string and returns its key in a string cache."] # [macro_export] macro_rules ! namespace_prefix { ("") => { $ crate :: ATOM_PREFIX_ } ;
("*") => { $ crate :: ATOM_PREFIX__2A } ;
("html") => { $ crate :: ATOM_PREFIX__68_74_6D_6C } ;
("mathml") => { $ crate :: ATOM_PREFIX__6D_61_74_68_6D_6C } ;
("svg") => { $ crate :: ATOM_PREFIX__73_76_67 } ;
("xlink") => { $ crate :: ATOM_PREFIX__78_6C_69_6E_6B } ;
("xml") => { $ crate :: ATOM_PREFIX__78_6D_6C } ;
("xmlns") => { $ crate :: ATOM_PREFIX__78_6D_6C_6E_73 } ;
}pub type Namespace = :: string_cache :: Atom < NamespaceStaticSet > ;
# [derive (PartialEq , Eq , PartialOrd , Ord)] pub struct NamespaceStaticSet ;
impl :: string_cache :: StaticAtomSet for NamespaceStaticSet { fn get () -> & 'static :: string_cache :: PhfStrSet { static SET : :: string_cache :: PhfStrSet = :: string_cache :: PhfStrSet { key : 15467950696543387533u64 , disps : & [(1u32 , 0u32) , (1u32 , 4u32)] , atoms : & ["http://www.w3.org/1999/xhtml",
"http://www.w3.org/2000/svg",
"http://www.w3.org/XML/1998/namespace",
"http://www.w3.org/1998/Math/MathML",
"http://www.w3.org/1999/xlink",
"",
"http://www.w3.org/2000/xmlns/"] , hashes : & [1895391709u32 , 3069293938u32 , 2770585642u32 , 1216229735u32 , 2535599242u32 , 811901650u32 , 830072559u32] } ;
& SET } fn empty_string_index () -> u32 { 5u32 } } pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_68_74_6D_6C : Namespace = Namespace :: pack_static (0u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_73_76_67 : Namespace = Namespace :: pack_static (1u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_58_4D_4C_2F_31_39_39_38_2F_6E_61_6D_65_73_70_61_63_65 : Namespace = Namespace :: pack_static (2u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_38_2F_4D_61_74_68_2F_4D_61_74_68_4D_4C : Namespace = Namespace :: pack_static (3u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_6C_69_6E_6B : Namespace = Namespace :: pack_static (4u32) ;
pub const ATOM_NAMESPACE_ : Namespace = Namespace :: pack_static (5u32) ;
pub const ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_78_6D_6C_6E_73_2F : Namespace = Namespace :: pack_static (6u32) ;
pub const ATOM_NAMESPACE__2A : Namespace = Namespace :: pack_inline (10752u64 , 1u8) ;
# [doc = "Takes a namespace url string and returns its key in a string cache."] # [macro_export] macro_rules ! namespace_url { ("http://www.w3.org/1999/xhtml") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_68_74_6D_6C } ;
("http://www.w3.org/2000/svg") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_73_76_67 } ;
("http://www.w3.org/XML/1998/namespace") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_58_4D_4C_2F_31_39_39_38_2F_6E_61_6D_65_73_70_61_63_65 } ;
("http://www.w3.org/1998/Math/MathML") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_38_2F_4D_61_74_68_2F_4D_61_74_68_4D_4C } ;
("http://www.w3.org/1999/xlink") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_31_39_39_39_2F_78_6C_69_6E_6B } ;
("") => { $ crate :: ATOM_NAMESPACE_ } ;
("http://www.w3.org/2000/xmlns/") => { $ crate :: ATOM_NAMESPACE__68_74_74_70_3A_2F_2F_77_77_77_2E_77_33_2E_6F_72_67_2F_32_30_30_30_2F_78_6D_6C_6E_73_2F } ;
("*") => { $ crate :: ATOM_NAMESPACE__2A } ;
}
        /// Maps the input of [`namespace_prefix!`](macro.namespace_prefix.html) to 
        /// the output of [`namespace_url!`](macro.namespace_url.html).
        ///
        #[macro_export] macro_rules! ns {
        
() => { namespace_url!("") };
(*) => { namespace_url!("*") };
(html) => { namespace_url!("http://www.w3.org/1999/xhtml") };
(xml) => { namespace_url!("http://www.w3.org/XML/1998/namespace") };
(xmlns) => { namespace_url!("http://www.w3.org/2000/xmlns/") };
(xlink) => { namespace_url!("http://www.w3.org/1999/xlink") };
(svg) => { namespace_url!("http://www.w3.org/2000/svg") };
(mathml) => { namespace_url!("http://www.w3.org/1998/Math/MathML") };
}
